# Rive Crash Prevention and Fixes

This document outlines the comprehensive fixes implemented to prevent Rive crashes on Android and improve overall
stability.

## Root Causes Identified

1. **Known Issues with rive-react-native**: Multiple reported crashes with certain versions
2. **Memory Management**: Multiple animations running simultaneously causing memory issues
3. **Threading Conflicts**: ExoPlayer crashes due to threading issues
4. **Missing Error Boundaries**: Lack of comprehensive error handling
5. **Network/Loading Issues**: Failed network requests causing crashes
6. **Proguard Obfuscation**: Native libraries being obfuscated incorrectly

## Implemented Fixes

### 1. Enhanced Rive Component (`src/components/atoms/Rive/Rive.native.tsx`)

**Features Added:**

- Comprehensive error handling with try-catch blocks
- Loading states and timeout handling (10-second default)
- Retry mechanisms (up to 3 retries by default)
- Proper cleanup on component unmount
- Memory leak prevention
- Crash reporting and analytics

**New Props:**

- `fallbackText`: Custom text when animation fails
- `enableRetry`: Enable/disable retry functionality
- `maxRetries`: Maximum number of retry attempts
- `timeout`: Loading timeout duration

### 2. Error Boundary (`src/components/atoms/Rive/RiveErrorBoundary.tsx`)

**Features:**

- Catches React errors in Rive components
- Provides fallback UI when crashes occur
- Tracks errors for monitoring
- Allows manual retry

**Features:**

- Combines enhanced Rive component with error boundary
- Default export for easy migration
- Maintains same API as original component

### 4. Memory Management (`src/core/managers/RiveManager.ts`)

**Features:**

- Limits concurrent animations (3 on Android, 5 on iOS)
- Automatic cleanup of old animations
- Memory usage monitoring
- Periodic cleanup (every 30 seconds)
- Animation lifecycle tracking

### 5. Crash Prevention System (`src/core/utils/riveCrashPrevention.ts`)

**Features:**

- Tracks crash frequency and implements cooldown periods
- Prevents animations after multiple crashes
- URL validation for Rive files
- Crash statistics and reporting
- Automatic recovery mechanisms

### 6. Version Checking (`src/core/utils/riveVersionCheck.ts`)

**Features:**

- Identifies problematic Rive versions
- Provides upgrade/downgrade recommendations
- Platform-specific version tracking
- Automated logging of version issues

### 7. Android-Specific Fixes

#### Proguard Rules (`android/app/proguard-rules.pro`)

```proguard
# Rive React Native
-keep class app.rive.runtime.** { *; }
-keep class com.rivereactnative.** { *; }
-dontwarn app.rive.runtime.**
-dontwarn com.rivereactnative.**

# Keep native methods for Rive
-keepclasseswithmembernames class * {
    native <methods>;
}
```

#### Gradle Configuration (`android/app/build.gradle`)

```gradle
packagingOptions {
    // Prevent conflicts with Rive native libraries
    pickFirst '**/libc++_shared.so'
    pickFirst '**/librive-android.so'
}
```

## Migration Guide

### Automatic Migration

The fixes are designed to be backward compatible. Simply update your imports:

```typescript
// Before
import Rive from 'atoms/Rive';

// After (automatically uses SafeRive)
import Rive from 'atoms/Rive';
```

### Advanced Usage

For more control, you can use specific components:

```typescript
import { RiveComponent, RiveErrorBoundary, withRiveErrorBoundary } from 'atoms/Rive';

// Use error boundary manually
<RiveErrorBoundary fallbackText = "Custom fallback" >
<RiveComponent url = "..." / >
  </RiveErrorBoundary>

// Use HOC pattern
const SafeCustomComponent = withRiveErrorBoundary(CustomRiveComponent);
```

### New Props Available

```typescript
<Rive
  url = "https://example.com/animation.riv"
fallbackText = "Animation not available"  // Custom fallback text
enableRetry = { true }                       // Enable retry button
maxRetries = { 3 }                          // Maximum retry attempts
timeout = { 10000 }                         // Loading timeout in ms
// ... other existing props
/>
```

## Monitoring and Analytics

### Tracked Events

- `rive_animation_registered`: When animation starts
- `rive_animation_unregistered`: When animation stops
- `rive_crash_prevented`: When crash prevention activates
- `rive_successful_load`: When animation loads successfully
- `rive_animation_retry`: When user retries failed animation
- `rive_version_check`: Version compatibility information

### Crash Statistics

Access crash statistics programmatically:

```typescript
import RiveCrashPrevention from 'core/utils/riveCrashPrevention';

const crashPrevention = RiveCrashPrevention.getInstance();
const stats = crashPrevention.getCrashStats();
console.log('Crash count:', stats.crashCount);
console.log('Can load animations:', stats.canLoadAnimations);
```

## Performance Optimizations

### Memory Management

- **Android**: Maximum 3 concurrent animations
- **iOS**: Maximum 5 concurrent animations
- Automatic cleanup of animations older than 5 minutes
- Periodic cleanup every 30 seconds

### Loading Optimizations

- 10-second timeout for animation loading
- Fallback UI during loading states
- Retry mechanism for failed loads
- URL validation before loading

## Troubleshooting

### Common Issues

1. **"Too many animations running"**
    - Reduce concurrent animations
    - Implement lazy loading
    - Use animation pooling

2. **"Animation temporarily disabled for stability"**
    - Wait for cooldown period (1 minute)
    - Check crash statistics
    - Consider using static images as fallback

3. **Loading timeouts**
    - Check network connectivity
    - Verify Rive file URLs
    - Increase timeout duration if needed

### Debug Information

Enable debug logging:

```typescript
import RiveVersionChecker from 'core/utils/riveVersionCheck';

const versionChecker = RiveVersionChecker.getInstance();
versionChecker.logVersionInfo(); // Logs version recommendations
```

## Best Practices

1. **Always use SafeRive** (default export) for new implementations
2. **Implement fallback UI** for critical animations
3. **Monitor crash statistics** in production
4. **Use lazy loading** for non-critical animations
5. **Test on low-end Android devices** before release
6. **Keep Rive files optimized** and small in size
7. **Implement proper error boundaries** at page level

## Future Improvements

1. **WebAssembly Integration**: Consider WASM for better performance
2. **Animation Preloading**: Implement smart preloading strategies
3. **CDN Optimization**: Use CDN for faster Rive file delivery
4. **A/B Testing**: Test different animation strategies
5. **Performance Monitoring**: Add detailed performance metrics

## Testing

### Unit Tests

Run existing tests to ensure compatibility:

```bash
npm test -- --testPathPattern=Rive
```

### Integration Tests

Test crash scenarios:

1. Network failures
2. Invalid URLs
3. Memory pressure
4. Rapid component mounting/unmounting

### Performance Tests

Monitor on low-end devices:

1. Memory usage
2. CPU usage
3. Animation smoothness
4. App stability

## Support

For issues related to these fixes:

1. Check crash statistics first
2. Review analytics events
3. Test with fallback UI
4. Consider version downgrade if needed
5. Report persistent issues with detailed logs
