import { Platform } from 'react-native';
import Analytics from 'core/analytics';

interface RiveInstance {
  id: string;
  url: string;
  ref: any;
  timestamp: number;
}

class RiveManager {
  private static instance: RiveManager;

  private activeAnimations: Map<string, RiveInstance> = new Map();

  private maxConcurrentAnimations = Platform.OS === 'android' ? 3 : 5; // Lower limit for Android

  private cleanupInterval: NodeJS.Timeout | null = null;

  private readonly CLEANUP_INTERVAL = 30000; // 30 seconds

  private readonly MAX_ANIMATION_AGE = 300000; // 5 minutes

  private constructor() {
    this.startCleanupInterval();
  }

  public static getInstance(): RiveManager {
    if (!RiveManager.instance) {
      RiveManager.instance = new RiveManager();
    }
    return RiveManager.instance;
  }

  public registerAnimation(id: string, url: string, ref: any): boolean {
    try {
      // Check if we're at the limit
      if (this.activeAnimations.size >= this.maxConcurrentAnimations) {
        this.cleanupOldestAnimations(1);
      }

      // Register the new animation
      this.activeAnimations.set(id, {
        id,
        url,
        ref,
        timestamp: Date.now(),
      });

      Analytics.track('rive_animation_registered', {
        animationId: id,
        url,
        activeCount: this.activeAnimations.size,
        platform: Platform.OS,
      });

      return true;
    } catch (error) {
      console.error('Error registering Rive animation:', error);
      return false;
    }
  }

  public unregisterAnimation(id: string): void {
    try {
      const animation = this.activeAnimations.get(id);
      if (animation) {
        // Cleanup the animation
        this.cleanupAnimation(animation);
        this.activeAnimations.delete(id);

        Analytics.track('rive_animation_unregistered', {
          animationId: id,
          activeCount: this.activeAnimations.size,
          platform: Platform.OS,
        });
      }
    } catch (error) {
      console.error('Error unregistering Rive animation:', error);
    }
  }

  public getActiveAnimationCount(): number {
    return this.activeAnimations.size;
  }

  public isAnimationActive(id: string): boolean {
    return this.activeAnimations.has(id);
  }

  public cleanupAllAnimations(): void {
    try {
      for (const animation of this.activeAnimations.values()) {
        this.cleanupAnimation(animation);
      }
      this.activeAnimations.clear();

      Analytics.track('rive_animations_cleanup_all', {
        platform: Platform.OS,
      });
    } catch (error) {
      console.error('Error cleaning up all Rive animations:', error);
    }
  }

  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.cleanupAllAnimations();
  }

  // Method to check if we should allow a new animation
  public shouldAllowNewAnimation(): boolean {
    return this.activeAnimations.size < this.maxConcurrentAnimations;
  }

  // Method to get memory usage info
  public getMemoryInfo(): {
    activeCount: number;
    maxAllowed: number;
    oldestAnimationAge: number;
  } {
    const now = Date.now();
    let oldestAge = 0;

    if (this.activeAnimations.size > 0) {
      const timestamps = Array.from(this.activeAnimations.values()).map(
        (a) => a.timestamp,
      );
      const oldestTimestamp = Math.min(...timestamps);
      oldestAge = now - oldestTimestamp;
    }

    return {
      activeCount: this.activeAnimations.size,
      maxAllowed: this.maxConcurrentAnimations,
      oldestAnimationAge: oldestAge,
    };
  }

  private cleanupAnimation(animation: RiveInstance): void {
    try {
      if (animation.ref) {
        // Try to stop and reset the animation
        if (typeof animation.ref.stop === 'function') {
          animation.ref.stop();
        }
        if (typeof animation.ref.reset === 'function') {
          animation.ref.reset();
        }
      }
    } catch (error) {
      console.warn(`Error cleaning up animation ${animation.id}:`, error);
    }
  }

  private cleanupOldestAnimations(count: number): void {
    try {
      const sortedAnimations = Array.from(this.activeAnimations.values()).sort(
        (a, b) => a.timestamp - b.timestamp,
      );

      for (let i = 0; i < Math.min(count, sortedAnimations.length); i++) {
        const animation = sortedAnimations[i];
        this.unregisterAnimation(animation.id);
      }
    } catch (error) {
      console.error('Error cleaning up oldest animations:', error);
    }
  }

  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.performPeriodicCleanup();
    }, this.CLEANUP_INTERVAL);
  }

  private performPeriodicCleanup(): void {
    try {
      const now = Date.now();
      const animationsToCleanup: string[] = [];

      for (const [id, animation] of this.activeAnimations.entries()) {
        if (now - animation.timestamp > this.MAX_ANIMATION_AGE) {
          animationsToCleanup.push(id);
        }
      }

      for (const id of animationsToCleanup) {
        this.unregisterAnimation(id);
      }

      if (animationsToCleanup.length > 0) {
        Analytics.track('rive_animations_periodic_cleanup', {
          cleanedCount: animationsToCleanup.length,
          remainingCount: this.activeAnimations.size,
          platform: Platform.OS,
        });
      }
    } catch (error) {
      console.error('Error during periodic cleanup:', error);
    }
  }
}

export default RiveManager;
