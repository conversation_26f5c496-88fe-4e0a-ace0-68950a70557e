import { Platform } from 'react-native';
import RiveManager from 'core/managers/RiveManager';
import RiveCrashPrevention from 'core/utils/riveCrashPrevention';
import RiveVersionChecker from 'core/utils/riveVersionCheck';
import Analytics from 'core/analytics';

interface RiveSetupConfig {
  enableCrashPrevention?: boolean;
  enableMemoryManagement?: boolean;
  enableVersionChecking?: boolean;
  maxConcurrentAnimations?: number;
  crashPreventionTimeout?: number;
}

const DEFAULT_CONFIG: Required<RiveSetupConfig> = {
  enableCrashPrevention: true,
  enableMemoryManagement: true,
  enableVersionChecking: true,
  maxConcurrentAnimations: Platform.OS === 'android' ? 3 : 5,
  crashPreventionTimeout: 10000,
};

class RiveSetup {
  private static initialized = false;
  private static config: Required<RiveSetupConfig>;

  public static initialize(userConfig: RiveSetupConfig = {}): void {
    if (this.initialized) {
      console.warn('Rive setup already initialized');
      return;
    }

    this.config = { ...DEFAULT_CONFIG, ...userConfig };

    try {
      this.setupCrashPrevention();
      this.setupMemoryManagement();
      this.performVersionCheck();
      this.trackInitialization();

      this.initialized = true;
      console.log('✅ Rive crash prevention system initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Rive crash prevention:', error);
      Analytics.track('rive: setup failed', {
        error: error?.toString(),
        platform: Platform.OS,
      });
    }
  }

  private static setupCrashPrevention(): void {
    if (!this.config.enableCrashPrevention) return;

    const crashPrevention = RiveCrashPrevention.getInstance({
      timeoutDuration: this.config.crashPreventionTimeout,
      enableCrashReporting: true,
      enableMemoryMonitoring: true,
      maxConcurrentAnimations: this.config.maxConcurrentAnimations,
    });

    console.log('🛡️ Rive crash prevention enabled');
  }

  private static setupMemoryManagement(): void {
    if (!this.config.enableMemoryManagement) return;

    const riveManager = RiveManager.getInstance();
    
    // Set up cleanup on app state changes
    if (Platform.OS === 'android') {
      // Android-specific memory management
      const { AppState } = require('react-native');
      
      AppState.addEventListener('change', (nextAppState: string) => {
        if (nextAppState === 'background') {
          // Cleanup animations when app goes to background
          riveManager.cleanupAllAnimations();
        }
      });
    }

    console.log('🧠 Rive memory management enabled');
  }

  private static performVersionCheck(): void {
    if (!this.config.enableVersionChecking) return;

    const versionChecker = RiveVersionChecker.getInstance();
    versionChecker.logVersionInfo();

    if (versionChecker.hasKnownIssues()) {
      console.warn('⚠️ Current Rive version has known issues. Check logs for recommendations.');
    }
  }

  private static trackInitialization(): void {
    Analytics.track('rive: crash prevention initialized', {
      config: this.config,
      platform: Platform.OS,
      timestamp: Date.now(),
    });
  }

  public static getConfig(): Required<RiveSetupConfig> {
    return { ...this.config };
  }

  public static isInitialized(): boolean {
    return this.initialized;
  }

  public static getSystemStatus(): {
    initialized: boolean;
    crashPreventionActive: boolean;
    memoryManagementActive: boolean;
    versionCheckComplete: boolean;
    activeAnimations: number;
    crashStats: any;
  } {
    if (!this.initialized) {
      return {
        initialized: false,
        crashPreventionActive: false,
        memoryManagementActive: false,
        versionCheckComplete: false,
        activeAnimations: 0,
        crashStats: null,
      };
    }

    const riveManager = RiveManager.getInstance();
    const crashPrevention = RiveCrashPrevention.getInstance();

    return {
      initialized: true,
      crashPreventionActive: this.config.enableCrashPrevention,
      memoryManagementActive: this.config.enableMemoryManagement,
      versionCheckComplete: this.config.enableVersionChecking,
      activeAnimations: riveManager.getActiveAnimationCount(),
      crashStats: crashPrevention.getCrashStats(),
    };
  }

  public static reset(): void {
    if (this.initialized) {
      const riveManager = RiveManager.getInstance();
      riveManager.cleanupAllAnimations();
      
      const crashPrevention = RiveCrashPrevention.getInstance();
      crashPrevention.resetCrashCount();

      this.initialized = false;
      console.log('🔄 Rive crash prevention system reset');
    }
  }
}

export default RiveSetup;
