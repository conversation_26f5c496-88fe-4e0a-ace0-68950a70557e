import { useEffect } from 'react';
import { GrowthBook, GrowthBookProvider } from '@growthbook/growthbook-react';
import userReader from 'core/readers/userReader';
import { useSession } from 'modules/auth/containers/AuthProvider';
import Analytics from 'core/analytics';

// Create a GrowthBook instance
const gb = new GrowthBook({
  apiHost: process.env.EXPO_PUBLIC_GROWTHBOOK_HOST,
  clientKey: process.env.EXPO_PUBLIC_GROWTHBOOK_CLIENT_KEY,
  enableDevMode: __DEV__,
  // Only required for A/B testing
  // Called every time a user is put into an experiment
  trackingCallback: (experiment, result) => {
    Analytics.track('$experiment_started', {
      'Experiment name': experiment.key,
      'Variant name': result.key ?? result.variationId,
      $source: 'growthbook',
    });
  },
});

gb.init();

const WithGrowthBookProvider = (props) => {
  const { children } = props;
  const { user } = useSession();

  useEffect(() => {
    gb.setAttributes({
      ...(user ?? EMPTY_OBJECT),
      id: Analytics.getMixpanelDistinctId() ?? userReader.id(user),
    });

    return () => {
      gb.destroy();
    };
  }, [user]);

  return <GrowthBookProvider growthbook={gb}>{children}</GrowthBookProvider>;
};

export default WithGrowthBookProvider;
