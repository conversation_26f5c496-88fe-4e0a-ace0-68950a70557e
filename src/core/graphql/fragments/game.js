/* eslint-disable import/prefer-default-export */
import { gql } from '@apollo/client';

export const GAME_FRAGMENT = gql`
  fragment CoreGameFields on Game {
    _id
    players {
      userId
      rating
      statikCoins
      status
      timeLeft
    }
    gameStatus
    rematchRequestedBy
    gameType
    createdBy
    config {
      timeLimit
      numPlayers
      maxTimePerQuestion
    }
    minifiedQuestions
    encryptedQuestions
    leaderBoard {
      userId
      correct
      incorrect
      totalPoints
      ratingChange
      statikCoinsEarned
      rank
    }
    startTime
    endTime
    seriesId
    showdownId
    showdownGameConfig {
      isRoundEnded
      hasOpponentNotShown
      nextGameId
      round
      nextGameStartsAt
      totalGamesPlayed
      showdownGamePlayer {
        isTie
        isWinner
        userId
        wins
        score
      }
      numOfGames
    }
  }
`;
