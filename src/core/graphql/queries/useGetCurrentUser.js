import { useCallback } from 'react';
import { gql, useLazyQuery } from '@apollo/client';

import { CURRENT_USER_FRAGMENT } from '../fragments/user';

const FETCH_CURRENT_USER_QUERY = gql`
  ${CURRENT_USER_FRAGMENT}
  query GetCurrentUser {
    user: getCurrentUser {
      ...CurrentUserFields
    }
  }
`;

const useGetCurrentUserQuery = () => {
  const [fetchUser, { data, loading, error }] = useLazyQuery(
    FETCH_CURRENT_USER_QUERY,
  );

  const fetchCurrentUser = useCallback(
    ({ queryOptions = EMPTY_OBJECT } = EMPTY_OBJECT) =>
      fetchUser({
        fetchPolicy: 'network-only',
        ...queryOptions,
      }),
    [fetchUser],
  );

  return {
    fetchCurrentUser,
    user: data?.user,
    loading,
    error,
  };
};

export default useGetCurrentUserQuery;
