/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect } from 'react';
import initWasm from '@/src/wasm';
import useUserStore from 'store/useUserStore';

const useLoadWasm = () => {
  const { isWasmReady, updateWasmReady } = useUserStore((state) => ({
    isWasmReady: state.isWasmReady,
    updateWasmReady: state.updateWasmReady,
  }));

  useEffect(() => {
    if (isWasmReady) return;
    (async () => {
      await initWasm();
      updateWasmReady(true);
    })();
  }, []);
};

export default useLoadWasm;
