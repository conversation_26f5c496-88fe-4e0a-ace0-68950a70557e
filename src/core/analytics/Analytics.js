import { Mixpanel } from 'mixpanel-react-native';
import _toString from 'lodash/toString';
import _isString from 'lodash/isString';
import crashlytics from './Crashlytics';

const trackAutomaticEvents = false;
const useNative = false;

class Analytics {
  constructor() {
    // Initialize Mixpanel
    this.mixpanel = new Mixpanel(
      process.env.EXPO_PUBLIC_MIX_PANEL_TOKEN,
      trackAutomaticEvents,
      useNative,
    );
    this.mixpanel.init(
      false,
      { track_pageview: true, persistence: 'localStorage' },
      'https://api.mixpanel.com',
    );
  }

  getMixpanelDistinctId() {
    return this.mixpanel?.getDistinctId?.();
  }

  // Track an event
  track(eventName, properties = {}) {
    if (!_isString(eventName)) {
      console.warn('Analytics.track: eventName is not a string');
      return;
    }
    // Track event with Mixpanel
    this.mixpanel.track(eventName, properties);

    webengage?.track?.(eventName, properties);
    crashlytics?.().log?.(eventName);
  }

  // Identify a user
  identify(userId) {
    // Identify user with Mixpanel
    this.mixpanel.identify(userId);

    // Identify user with webengage
    webengage?.user?.login?.(userId);
    crashlytics?.().setUserId?.(userId);
  }

  logout() {
    webengage?.user?.logout?.();
    this.mixpanel.reset();
  }

  // Set user properties
  setUserProperties(properties = {}) {
    // Set user properties in Mixpanel
    this.mixpanel.getPeople().set(properties);

    // Set user properties in webengage
    Object.keys(properties).forEach((key) => {
      const value = properties[key];
      webengage?.user?.setAttribute?.(key, value);
      crashlytics?.()?.setAttribute?.(key, _toString(value));
    });
  }

  setUserPropertiesToMixpanel(properties = EMPTY_OBJECT) {
    // Set user properties in Mixpanel
    this.mixpanel.getPeople().set(properties);
  }

  reset() {
    this.mixpanel.reset();

    webengage?.user?.logout?.();
  }
}

const analytics = new Analytics();

export default analytics;
