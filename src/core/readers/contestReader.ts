import _property from 'lodash/property';
import _get from 'lodash/get';

const contestReader = {
  id: _property('_id'),
  name: _property('name'),
  startTime: _property('startTime'),
  endTime: _property('endTime'),
  endTimeMs: (contest: any) => new Date(_get(contest, 'endTime')).getTime(),
  startTimeMs: (contest: any) => new Date(_get(contest, 'startTime')).getTime(),
  questions: _property('questions'),
  registrationCount: _property('registrationCount'),
  status: _property('status'),
  currentUserParticipation: _property('currentUserParticipation'),
  recentParticipants: _property('recentParticipants'),
  contestDuration: _property('contestDuration'),
  description: _property('description'),
  formFields: (contest: any) =>
    _get(contest, ['registrationForm', 'fields'], []),
};

export default contestReader;
