import { Platform } from 'react-native';
import Analytics from 'core/analytics';

interface RiveCrashPreventionConfig {
  maxConcurrentAnimations: number;
  enableMemoryMonitoring: boolean;
  enableCrashReporting: boolean;
  timeoutDuration: number;
}

const DEFAULT_CONFIG: RiveCrashPreventionConfig = {
  maxConcurrentAnimations: Platform.OS === 'android' ? 3 : 5,
  enableMemoryMonitoring: true,
  enableCrashReporting: true,
  timeoutDuration: 10000,
};

class RiveCrashPrevention {
  private static instance: RiveCrashPrevention;
  private config: RiveCrashPreventionConfig;
  private crashCount: number = 0;
  private lastCrashTime: number = 0;
  private readonly CRASH_COOLDOWN = 60000; // 1 minute
  private readonly MAX_CRASHES_PER_SESSION = 5;

  private constructor(config: Partial<RiveCrashPreventionConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  public static getInstance(config?: Partial<RiveCrashPreventionConfig>): RiveCrashPrevention {
    if (!RiveCrashPrevention.instance) {
      RiveCrashPrevention.instance = new RiveCrashPrevention(config);
    }
    return RiveCrashPrevention.instance;
  }

  public shouldAllowAnimation(url: string): boolean {
    const now = Date.now();

    // Check if we're in crash cooldown
    if (this.isInCrashCooldown(now)) {
      this.reportPreventedAnimation('crash_cooldown', url);
      return false;
    }

    // Check if we've exceeded max crashes
    if (this.crashCount >= this.MAX_CRASHES_PER_SESSION) {
      this.reportPreventedAnimation('max_crashes_exceeded', url);
      return false;
    }

    // Check URL validity
    if (!this.isValidRiveUrl(url)) {
      this.reportPreventedAnimation('invalid_url', url);
      return false;
    }

    return true;
  }

  public reportCrash(error: any, url: string, context?: any): void {
    const now = Date.now();
    this.crashCount++;
    this.lastCrashTime = now;

    if (this.config.enableCrashReporting) {
      Analytics.track('rive: crash prevented', {
        error: error?.message || error?.toString() || 'Unknown error',
        url,
        crashCount: this.crashCount,
        platform: Platform.OS,
        context: context || {},
        timestamp: now,
      });
    }

    console.error('Rive crash reported:', {
      error,
      url,
      crashCount: this.crashCount,
      context,
    });
  }

  public reportSuccessfulLoad(url: string): void {
    // Reset crash count on successful loads (with some decay)
    if (this.crashCount > 0) {
      this.crashCount = Math.max(0, this.crashCount - 0.5);
    }

    if (this.config.enableCrashReporting) {
      Analytics.track('rive_successful_load', {
        url,
        remainingCrashCount: this.crashCount,
        platform: Platform.OS,
      });
    }
  }

  public getConfig(): RiveCrashPreventionConfig {
    return { ...this.config };
  }

  public updateConfig(newConfig: Partial<RiveCrashPreventionConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  public getCrashStats(): {
    crashCount: number;
    lastCrashTime: number;
    isInCooldown: boolean;
    canLoadAnimations: boolean;
  } {
    const now = Date.now();
    return {
      crashCount: this.crashCount,
      lastCrashTime: this.lastCrashTime,
      isInCooldown: this.isInCrashCooldown(now),
      canLoadAnimations: this.crashCount < this.MAX_CRASHES_PER_SESSION && !this.isInCrashCooldown(now),
    };
  }

  public resetCrashCount(): void {
    this.crashCount = 0;
    this.lastCrashTime = 0;

    Analytics.track('rive_crash_count_reset', {
      platform: Platform.OS,
    });
  }

  private isInCrashCooldown(now: number): boolean {
    return this.lastCrashTime > 0 && (now - this.lastCrashTime) < this.CRASH_COOLDOWN;
  }

  private isValidRiveUrl(url: string): boolean {
    try {
      // Basic URL validation
      if (!url || typeof url !== 'string') {
        return false;
      }

      // Check if it's a valid URL
      new URL(url);

      // Check if it's a .riv file
      return url.toLowerCase().includes('.riv');
    } catch {
      return false;
    }
  }

  private reportPreventedAnimation(reason: string, url: string): void {
    if (this.config.enableCrashReporting) {
      Analytics.track('rive_animation_prevented', {
        reason,
        url,
        crashCount: this.crashCount,
        platform: Platform.OS,
      });
    }
  }
}

export default RiveCrashPrevention;
