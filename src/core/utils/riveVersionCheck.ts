import { Platform } from 'react-native';
import Analytics from 'core/analytics';

interface RiveVersionInfo {
  currentVersion: string;
  recommendedVersion: string;
  hasKnownIssues: boolean;
  issues: string[];
  recommendations: string[];
}

class RiveVersionChecker {
  private static instance: RiveVersionChecker;

  // Known problematic versions
  private readonly PROBLEMATIC_VERSIONS = {
    android: [
      '7.3.0', // Known crash issues
      '8.0.0', // Initial release issues
    ],
    ios: [
      '7.3.0', // Known crash issues
    ],
  };

  // Recommended stable versions
  private readonly RECOMMENDED_VERSIONS = {
    android: '8.1.0', // Current stable
    ios: '8.1.0',
  };

  private constructor() {}

  public static getInstance(): RiveVersionChecker {
    if (!RiveVersionChecker.instance) {
      RiveVersionChecker.instance = new RiveVersionChecker();
    }
    return RiveVersionChecker.instance;
  }

  public checkCurrentVersion(): RiveVersionInfo {
    // In a real implementation, you would get this from package.json
    // For now, we'll use the version from your package.json
    const currentVersion = '8.1.0';
    const platform = Platform.OS as 'android' | 'ios';

    const problematicVersions = this.PROBLEMATIC_VERSIONS[platform] || [];
    const recommendedVersion = this.RECOMMENDED_VERSIONS[platform];

    const hasKnownIssues = problematicVersions.includes(currentVersion);

    const issues: string[] = [];
    const recommendations: string[] = [];

    if (hasKnownIssues) {
      issues.push(`Version ${currentVersion} has known stability issues on ${platform}`);
      recommendations.push(`Consider downgrading to version ${recommendedVersion}`);
    }

    if (currentVersion !== recommendedVersion) {
      if (!hasKnownIssues) {
        recommendations.push(`Consider using recommended version ${recommendedVersion} for optimal stability`);
      }
    }

    const versionInfo: RiveVersionInfo = {
      currentVersion,
      recommendedVersion,
      hasKnownIssues,
      issues,
      recommendations,
    };

    // Track version info
    Analytics.track('rive: version check', {
      ...versionInfo,
      platform,
    });

    return versionInfo;
  }

  public getVersionRecommendations(): string[] {
    const versionInfo = this.checkCurrentVersion();
    return versionInfo.recommendations;
  }

  public hasKnownIssues(): boolean {
    const versionInfo = this.checkCurrentVersion();
    return versionInfo.hasKnownIssues;
  }

  public logVersionInfo(): void {
    const versionInfo = this.checkCurrentVersion();

    console.log('Rive Version Check:', versionInfo);

    if (versionInfo.hasKnownIssues) {
      console.warn('⚠️ Rive Version Issues Detected:');
      versionInfo.issues.forEach(issue => console.warn(`  - ${issue}`));
    }

    if (versionInfo.recommendations.length > 0) {
      console.log('💡 Rive Version Recommendations:');
      versionInfo.recommendations.forEach(rec => console.log(`  - ${rec}`));
    }
  }
}

export default RiveVersionChecker;
