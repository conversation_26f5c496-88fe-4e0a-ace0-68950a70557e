import _round from 'lodash/round';
import _get from 'lodash/get';
import { EXPONENT_FIELDS_KEYS } from 'modules/practice/constants/fieldKeys';
import _isNil from 'lodash/isNil';
import _toString from 'lodash/toString';
import { BaseConfig, BasePreset, BaseQuestionType } from './types';
import { getRandomIntInclusive } from './common';

interface ExponentConfig extends BaseConfig {
  [EXPONENT_FIELDS_KEYS.DIGITS_IN_BASE]: number;
  [EXPONENT_FIELDS_KEYS.MAX_EXPONENT]: number;
  [EXPONENT_FIELDS_KEYS.DECIMALS_IN_EXPONENT]: number;
  [EXPONENT_FIELDS_KEYS.PRECISION_IN_ANSWER]?: number;
}

interface ExponentPreset extends BasePreset {
  config: ExponentConfig;
}

export interface ExponentQuestionType extends BaseQuestionType {
  base: number;
  exponent: number;
  answers: number[];
  digitsInBase: number;
  maxExponent: number;
  decimalsInExponent: number;
}

export const generateExponentQuestion = ({
  preset,
}: {
  preset: ExponentPreset;
}): ExponentQuestionType => {
  const { config } = preset;

  const digitsInBase = _get(config, [EXPONENT_FIELDS_KEYS.DIGITS_IN_BASE], 2);
  const exponentValue = _get(config, [EXPONENT_FIELDS_KEYS.MAX_EXPONENT], 2);
  const decimalsInExponent = _get(
    config,
    [EXPONENT_FIELDS_KEYS.DECIMALS_IN_EXPONENT],
    2,
  );
  const precisionValue = _get(config, [
    EXPONENT_FIELDS_KEYS.PRECISION_IN_ANSWER,
  ]);

  const minBase = 10 ** (digitsInBase - 1);
  const maxBase = 10 ** digitsInBase - 1;
  const base = getRandomIntInclusive(minBase, maxBase);

  const isDecimalExponent = decimalsInExponent > 0;
  let exponent;

  if (isDecimalExponent) {
    const minDecimal = 10 ** (decimalsInExponent - 1);
    const maxDecimal = 10 ** decimalsInExponent - 1;
    const decimalValue = getRandomIntInclusive(minDecimal, maxDecimal);
    exponent = parseFloat(`${exponentValue}.${decimalValue}`);
  } else {
    exponent = exponentValue;
  }

  let answer = base ** exponent;

  if (decimalsInExponent > 0 && !_isNil(precisionValue)) {
    answer = _round(answer, precisionValue);
    answer = parseFloat(_toString(answer));
  }

  return {
    base,
    exponent,
    answers: [answer],
    digitsInBase,
    maxExponent: exponentValue,
    decimalsInExponent,
  };
};
