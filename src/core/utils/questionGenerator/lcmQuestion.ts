import _get from 'lodash/get';
import _filter from 'lodash/filter';
import { LCM_FIELDS_KEYS } from 'modules/practice/constants/fieldKeys';
import { BaseConfig, BasePreset, BaseQuestionType } from './types';
import { getRandomIntInclusive } from './common';
import { gcd } from './hcfQuestion';

export interface LcmQuestionType extends BaseQuestionType {
  expression: string;
  answers: number[];
  firstNumber: number;
  secondNumber: number;
}

interface BaseRange {
  min: number;
  max: number;
}

interface LcmConfig extends BaseConfig {
  [LCM_FIELDS_KEYS.FIRST_NO_DIGITS]: BaseRange;
  [LCM_FIELDS_KEYS.SECOND_NO_DIGITS]: BaseRange;
}

interface LcmPreset extends BasePreset {
  config: LcmConfig;
}

const lcmTwo = (a: number, b: number): number => {
  const absA = Math.abs(a);
  const absB = Math.abs(b);
  if (absA === 0 || absB === 0) return 0;
  const commonDivisor = gcd(absA, absB);
  return commonDivisor === 0 ? 0 : (absA * absB) / commonDivisor;
};

const lcmArray = (arr: number[]): number => {
  if (!arr || arr.length === 0) return 1;
  const positiveNumbers = _filter(arr, (n) => n > 0);
  if (positiveNumbers.length === 0) return 0;
  if (positiveNumbers.length === 1) return positiveNumbers[0];

  let result = positiveNumbers[0];
  for (let i = 1; i < positiveNumbers.length; i++) {
    result = lcmTwo(result, positiveNumbers[i]);
  }

  return result;
};

export const generateLcmQuestion = ({
  preset,
}: {
  preset: LcmPreset;
}): LcmQuestionType => {
  const config = preset.config;
  const firstNoDigits = _get(config, LCM_FIELDS_KEYS.FIRST_NO_DIGITS, {
    min: 1,
    max: 10,
  });
  const secondNoDigits = _get(config, LCM_FIELDS_KEYS.SECOND_NO_DIGITS, {
    min: 1,
    max: 10,
  });

  let expression: string;

  let firstNumber;
  let secondNumber;

  do {
    firstNumber = getRandomIntInclusive(firstNoDigits.min, firstNoDigits.max);
    secondNumber = getRandomIntInclusive(
      secondNoDigits.min,
      secondNoDigits.max,
    );
  } while (firstNumber === secondNumber);

  expression = `${firstNumber} LCM ${secondNumber}`;

  const answer = lcmArray([firstNumber, secondNumber]);

  return {
    expression,
    answers: [answer],
    firstNumber,
    secondNumber,
  };
};
