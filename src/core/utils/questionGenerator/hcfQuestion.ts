import { HCF_FIELDS_KEYS } from '@/src/modules/practice/constants/fieldKeys';
import { BaseConfig, BasePreset, BaseQuestionType } from './types';
import _get from 'lodash/get';
import { getRandomIntInclusive } from './common';

interface BaseRange {
  min: number;
  max: number;
}

interface HcfConfig extends BaseConfig {
  [HCF_FIELDS_KEYS.FIRST_NO_DIGITS]: BaseRange;
  [HCF_FIELDS_KEYS.SECOND_NO_DIGITS]: BaseRange;
}

interface HcfPreset extends BasePreset {
  config: HcfConfig;
}

export interface HcfQuestionType extends BaseQuestionType {
  expression: string;
  answers: number[];
  firstNumber: number;
  secondNumber: number;
}

export const gcd = (a: number, b: number): number => {
  a = Math.abs(a);
  b = Math.abs(b);
  while (b) {
    a %= b;
    [a, b] = [b, a];
  }
  return a;
};

export const generateHcfQuestion = ({
  preset,
}: {
  preset: HcfPreset;
}): HcfQuestionType => {
  const config = preset.config;
  const firstNoDigits = _get(config, HCF_FIELDS_KEYS.FIRST_NO_DIGITS, {
    min: 1,
    max: 10,
  });
  const secondNoDigits = _get(config, HCF_FIELDS_KEYS.SECOND_NO_DIGITS, {
    min: 1,
    max: 10,
  });

  let expression: string;

  let firstNumber;
  let secondNumber;

  do {
    firstNumber = getRandomIntInclusive(firstNoDigits.min, firstNoDigits.max);
    secondNumber = getRandomIntInclusive(
      secondNoDigits.min,
      secondNoDigits.max,
    );
  } while (firstNumber === secondNumber);

  expression = `${firstNumber} HCF ${secondNumber}`;

  const hcf = gcd(firstNumber, secondNumber);

  return {
    expression,
    answers: [hcf],
    firstNumber,
    secondNumber,
  };
};
