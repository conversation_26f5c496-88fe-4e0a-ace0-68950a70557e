import _forEach from 'lodash/forEach';
import _isNil from 'lodash/isNil';
import _find from 'lodash/find';
import _isEqual from 'lodash/isEqual';
import {
  PRACTICE_CATEGORIES,
  PRACTICE_LEFT_PANE_TABS_CONFIGS,
} from '../../../modules/practice/pages/Practice/components/OperatorSelector/constants/practice';

import { generateMultiplicationQuestion } from './multiplicationQuestion';
import { generateDivisionQuestion } from './divisionQuestion';
import { generateAdditionQuestion } from './additionQuestion';
import { generateNthRootQuestion } from './nthRootQuestion';
import { generateExponentQuestion } from './exponentQuestion';
import { generateModQuestion } from './modQuestions';
import { BasePreset, BaseQuestionResult } from './types';
import { generateLcmQuestion } from './lcmQuestion';
import { generateHcfQuestion } from './hcfQuestion';

type QuestionGeneratorFunction = (args: {
  preset: BasePreset;
}) => BaseQuestionResult;

interface QuestionGeneratorFactory {
  [key: string]: QuestionGeneratorFunction;
}

const QUESTION_GENERATOR_FACTORY: QuestionGeneratorFactory = {
  [PRACTICE_CATEGORIES.MULTIPLICATION]: generateMultiplicationQuestion,
  [PRACTICE_CATEGORIES.DIVISION]: generateDivisionQuestion,
  [PRACTICE_CATEGORIES.ADD_AND_SUBSTRACT]: generateAdditionQuestion,
  [PRACTICE_CATEGORIES.ROOT]: generateNthRootQuestion,
  [PRACTICE_CATEGORIES.EXPONENT]: generateExponentQuestion,
  [PRACTICE_CATEGORIES.MOD]: generateModQuestion,
  [PRACTICE_CATEGORIES.LCM]: generateLcmQuestion,
  [PRACTICE_CATEGORIES.HCF]: generateHcfQuestion,
};

interface GeneratedQuestion extends BaseQuestionResult {
  typingDirection?: string;
  rows?: number;
  tag?: string;
  category: string;
  id?: string;
  presetId?: string;
}

const generateMathQuestion = (preset: BasePreset): GeneratedQuestion => {
  const { config, categoryId } = preset;
  const { rows, typingDirection } = config;

  const { tag: operationTag } =
    PRACTICE_LEFT_PANE_TABS_CONFIGS[categoryId] ?? {};

  const questionGenerator = QUESTION_GENERATOR_FACTORY[operationTag];

  if (_isNil(questionGenerator)) return {} as GeneratedQuestion;

  const questionObject = questionGenerator?.({ preset });

  return {
    ...questionObject,
    typingDirection,
    rows,
    tag: operationTag,
    category: categoryId,
  };
};

export const generateMathQuestions = (
  selectedPresets: BasePreset[],
): GeneratedQuestion[] => {
  const questions: GeneratedQuestion[] = [];
  const questionsIdentifier: (string | string[])[] = [];

  _forEach(selectedPresets, (preset) => {
    for (let i = 0; i < (preset?.config?.noOfQuestions ?? 0); i++) {
      let generatedQuestion = generateMathQuestion(preset);
      let { question } = generatedQuestion;
      let questionEntry = _find(questionsIdentifier, (identifier) =>
        _isEqual(identifier, question),
      );

      let retryCount = 0;

      while (!_isNil(questionEntry)) {
        if (retryCount >= 100) {
          return questions;
        }
        retryCount++;
        generatedQuestion = generateMathQuestion(preset);
        question = generatedQuestion.question;
        questionEntry = _find(questionsIdentifier, (identifier) =>
          _isEqual(identifier, question),
        );
      }

      if (!_isNil(question)) {
        questionsIdentifier.push(question);
      }
      questions.push({
        ...generatedQuestion,
        id: `question_${i + 1}`,
        presetId: preset?.id,
      });
    }
  });

  return questions;
};
