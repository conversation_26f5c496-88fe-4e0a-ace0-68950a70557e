import { gql, useMutation } from "@apollo/client";
import { useCallback } from "react";
import { GAME_TYPES } from "../../../home/<USER>/gameTypes";

const CHALLENGE_FRIEND = gql`
mutation ChallengeUser($challengeUserInput: ChallengeUserInput) {
  challengeUser(challengeUserInput: $challengeUserInput) {
    _id
    players {
      userId
      rating
      status
    }
    gameStatus
    rematchRequestedBy
    gameType
    createdBy
    config {
      timeLimit
      numPlayers
    }
    questions {
      question {
        id
        expression
        description
        options
        answers
        questionType
        rating
        maxTimeLimit
        tags
        fastestTimeTaken
      }
      submissions {
        userId
        timeTaken
        points
        submissionTime
        isCorrect
        submittedValues
      }
      stats {
        fastestTime
        userIds
      }
    }
    encryptedQuestions
    leaderBoard {
      userId
      correct
      incorrect
      totalPoints
      ratingChange
      rank
    }
    startTime
    endTime
    seriesId
  }
}
`;

const useChallengeUser = () => {

  const [challengeUserQuery] = useMutation(CHALLENGE_FRIEND)

  const challengeUser = useCallback(async ({ userId, gameConfig }) => {

    const selectedGameConfig = gameConfig ?? { timeLimit: 60, numPlayers: 2, gameType: GAME_TYPES.ONLINE_CHALLENGE }

    const response = await challengeUserQuery({
      variables: {
        challengeUserInput: {
          userId,
          gameConfig: selectedGameConfig
        }
      }
    })

    return response
  }, [challengeUserQuery])

  return {
    challengeUser
  }
};

export default useChallengeUser
