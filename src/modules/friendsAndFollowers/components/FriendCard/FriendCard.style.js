import { StyleSheet } from 'react-native'
import useMediaQuery from 'core/hooks/useMediaQuery'
import { useMemo } from 'react'
import dark from 'core/constants/themes/dark'

const createStyles = (isCompactMode) =>
    StyleSheet.create({
        container: {
            paddingVertical: 12,
            flexDirection: 'row',
            justifyContent: 'space-between',
            // paddingBottom:15,
            borderBottomColor: dark.colors.tertiary,
            borderBottomWidth: 1,
        },
        userImageContainer: {
            height: 40,
            width: 40,
            borderRadius: 20,
            borderWidth: 1,
            borderColor: dark.colors.textLight
        },
        userImage: {
            width: 38,
            height: 38,
            borderRadius: 20,
        },
        statusIcon: {
            position: 'absolute',
            width: 12,
            height: 12,
            backgroundColor: dark.colors.secondary,
            zIndex: 10,
            borderRadius: 30,
            bottom: 0,
            right: 0,
            borderWidth: 2,
            borderColor: dark.colors.background

        },
        userName: {
            fontFamily: 'Montserrat-500',
            fontSize: 14,
            lineHeight: 17,
            color: 'white',
            maxWidth: 175,
        },
        userRating: {
            fontFamily: 'Montserrat-500',
            fontSize: 12,
            lineHeight: 15,
            maxWidth: 175,
            color: dark.colors.textDark,
        },

        userInfoWithImage: {
            flexDirection: 'row',
            gap: 10,
            alignItems: 'flex-start',
            justifyContent: 'center',
        },
        userInfo: {
            gap: 4,
        },
        overlayStyle: {
            borderRadius: 8,
            padding: 0,
            position: 'absolute',
            maxWidth: 160,
            maxHeight: 45,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: dark.colors.primary,
        },
        challengeContainer: {
            flexDirection: 'row',
            gap: 10,
            alignItems: 'center',
            justifyContent: 'flex-end',
            flex: 1,
        },
        findFriendButton: {
            borderWidth: 0.8,
            borderColor: dark.colors.tertiary,
            borderRadius: 8,
            height: 32
        },
        findFriendButtonContianer: {
            width: 70,
            justifyContent: 'center',
            alignItems: 'center',
            borderRadius: 12,
        },
        findFriendText: {
            fontSize: 10,
            fontFamily: 'Montserrat-600',
            lineHeight: 17,
            color: dark.colors.secondary,
        },
        findFriendBackground: {
            width: 70,
            backgroundColor: dark.colors.tertiary,
            height: 32,
            bottom: -3
        },
    })

const useFriendCardStyles = () => {
    const { isMobile } = useMediaQuery()

    const styles = useMemo(() => createStyles(isMobile), [isMobile])

    return styles
}

export default useFriendCardStyles
