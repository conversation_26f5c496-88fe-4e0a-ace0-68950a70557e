import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import PropTypes from 'prop-types';
import { useRouter } from 'expo-router';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { showPopover } from 'molecules/Popover/Popover';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import Entypo from '@expo/vector-icons/Entypo';
import useMediaQuery from 'core/hooks/useMediaQuery';
import USER_ACTIVITY from '@/src/core/constants/userActivityConstants';
import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import userReader from 'core/readers/userReader';
import Analytics from '../../../../core/analytics/index';
import { ANALYTICS_EVENTS } from '../../../../core/analytics/const';
import useChallengeUser from '../../hooks/mutations/useChallengeUser';
import dark from '../../../../core/constants/themes/dark';
import FriendCardOptionsPopover from '../FriendCardOptionsPopover/FriendCardOptionsPopover';
import useFriendCardStyles from './FriendCard.style';
import { useRemoveFriend } from '../../hooks/mutations/useRemoveFriend';
import useGetMessageGroupForFriends from '../../hooks/queries/useGetMessageGroupForFriends';

const FriendCard = (props) => {
  const { infoData, onRemove, isOnline, currActivity } = props;
  const { isMobile: isCompactMode } = useMediaQuery();
  const { user: currentUser } = useSession();
  const { _id: currentUserId } = currentUser ?? EMPTY_OBJECT;
  const router = useRouter();
  const styles = useFriendCardStyles();
  const { removeFriend } = useRemoveFriend();
  const { challengeUser } = useChallengeUser();
  const [messageGroupLoading, setMessageGroupLoading] = useState(false);
  const cardRef = useRef();

  const [overlayStyle, setOverlayStyle] = useState(styles.overlayStyle);

  const onCardLayoutChange = useCallback(() => {
    cardRef.current?.measure((x, y, width, height, pageX, pageY) => {
      setOverlayStyle([
        styles.overlayStyle,
        { top: pageY, right: isCompactMode ? pageX : pageX + 180 },
      ]);
    });
  }, [cardRef, setOverlayStyle, isCompactMode]);

  const [isRemovingFriend, setIsRemovingFriend] = useState(false);

  const [isChallengingFriend, setIsChallengingFriend] = useState(false);

  const { getMessageGroupIDForFriends } = useGetMessageGroupForFriends();

  const userDetailsData = useMemo(
    () => infoData?.friendInfo,
    [infoData, currentUserId],
  );

  const {
    username,
    profileImageUrl,
    rating,
    _id: senderId,
    name,
  } = userDetailsData ?? EMPTY_OBJECT;

  const handleOnRemoveFriendPressed = useCallback(async () => {
    if (isRemovingFriend) {
      return;
    }
    try {
      Analytics.track(
        ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_REMOVE_FRIEND,
      );
      setIsRemovingFriend(true);
      await removeFriend({ receiverId: senderId });
      onRemove?.();
      showToast({
        type: TOAST_TYPE.SUCCESS,
        description: `Successfully Removed  ${username} from your friends list `,
      });
    } catch (e) {
      setIsRemovingFriend(false);
    } finally {
      setIsRemovingFriend(false);
    }
  }, [
    senderId,
    isRemovingFriend,
    setIsRemovingFriend,
    onRemove,
    removeFriend,
    username,
  ]);

  const onPressOptions = useCallback(() => {
    showPopover({
      content: (
        <FriendCardOptionsPopover
          onRemoveFriendPressed={handleOnRemoveFriendPressed}
        />
      ),
      style: overlayStyle,
    });
  }, [handleOnRemoveFriendPressed, overlayStyle]);

  const navigateToUserProfile = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS
        .CLICKED_ON_USER_PROFILE_FROM_FRIENDS_TAB,
    );
    router.push(`/profile/${username}`);
  }, [username, router]);

  const navigateToCreateLobby = useCallback(() => {
    router.push(`/games/lobby?friendId=${senderId}`);
  }, [senderId, router]);

  const handleOnChallengeUserPressed = useCallback(async () => {
    if (isChallengingFriend) {
      return;
    }
    try {
      Analytics.track(
        ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_CHALLENGE_FRIEND,
      );
      setIsChallengingFriend(true);
      await challengeUser({ userId: senderId });
    } catch (e) {
      setIsChallengingFriend(false);
    } finally {
      setIsChallengingFriend(false);
    }
  }, [setIsChallengingFriend, senderId, isChallengingFriend]);

  const routeToChat = useCallback(async () => {
    setMessageGroupLoading(true);
    try {
      const { data } = await getMessageGroupIDForFriends({
        friendID: senderId,
      });
      Analytics.track(
        ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_USER_CHAT_ACTION,
        {
          currentUserId: userReader.id(currentUser),
          friendUserId: senderId,
        },
      );
      setMessageGroupLoading(false);
      router.push(`/chat?messageGroupId=${data.getMessageGroupIdForFriends}`);
    } catch (e) {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: `Something went wrong. Please try again later`,
      });
    } finally {
      setMessageGroupLoading(false);
    }
  }, [getMessageGroupIDForFriends, senderId]);

  const isExploring = useMemo(
    () => currActivity === USER_ACTIVITY.EXPLORING,
    [currActivity],
  );

  return (
    <View style={styles.container} ref={cardRef} onLayout={onCardLayoutChange}>
      <TouchableOpacity
        style={styles.userInfoWithImage}
        onPress={navigateToUserProfile}
      >
        <View style={styles.userImageContainer}>
          <Image
            source={{ uri: profileImageUrl }}
            resizeMode="cover"
            style={styles.userImage}
          />
          {isOnline && (
            <View
              style={[
                styles.statusIcon,
                !isExploring && {
                  backgroundColor: dark.colors.inGameIndicator,
                },
              ]}
            />
          )}
        </View>
        <View style={styles.userInfo}>
          <Text style={styles.userName} numberOfLines={1}>{username}</Text>
          <Text style={styles.userRating}>{`${name} `}</Text>
        </View>
      </TouchableOpacity>

      <View style={[styles.challengeContainer]}>
        {isOnline && (
          <InteractivePrimaryButton
            onPress={navigateToCreateLobby}
            label="Challenge"
            buttonStyle={styles.findFriendButton}
            buttonContainerStyle={styles.findFriendButtonContianer}
            labelStyle={styles.findFriendText}
            buttonBorderBackgroundStyle={styles.findFriendBackground}
            buttonContentStyles={{ paddingVertical: 0 }}
          />
        )}
        <MaterialIcons
          name="chat-bubble-outline"
          size={18}
          color={dark.colors.textDark}
          onPress={routeToChat}
        />
        <Entypo
          name="dots-three-vertical"
          size={14}
          color={dark.colors.textDark}
          onPress={onPressOptions}
        />
      </View>
    </View>
  );
};

FriendCard.propTypes = {
  infoData: PropTypes.object,
  onRemove: PropTypes.func,
  isOnline: PropTypes.bool,
  currActivity: PropTypes.string,
};

export default React.memo(FriendCard);
