import { useSession } from '@/src/modules/auth/containers/AuthProvider'
import { useCallback } from 'react'
import userReader from 'core/readers/userReader'
import gameReader from 'core/readers/gameReader'

const useGameOwner = () => {
  const { user = EMPTY_OBJECT } = useSession()

  const checkIsGameOwner = useCallback(
    ({ game }) => userReader.id(user) === gameReader.createdBy(game),
    [user]
  )

  return {
    checkIsGameOwner,
  }
}

export default useGameOwner
