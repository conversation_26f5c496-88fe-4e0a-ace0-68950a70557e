import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import _find from 'lodash/find';
import _toString from 'lodash/toString';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _reduce from 'lodash/reduce';
import _filter from 'lodash/filter';
import _isEqual from 'lodash/isEqual';
import {useSession} from '@/src/modules/auth/containers/AuthProvider';
import _values from 'lodash/values';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import _findIndex from 'lodash/findIndex';
import useGameContext from './useGameContext';
import {EACH_QUESTION_RESULT_TIME} from "../constants/game";
import _toNumber from "lodash/toNumber";

const EMPTY_ARRAY = [];
const WAITING_TIME = 5;

const calculateCurrentQuestionIndex = (
  gameStartTime,
  timePerQuestion,
  waitingTime,
) => {
  const currentTime = getCurrentTimeWithOffset();
  const timeElapsed = currentTime - gameStartTime;
  const totalTimePerQuestion = (timePerQuestion + waitingTime) * 1000;
  return Math.floor(timeElapsed / totalTimePerQuestion);
};

const useGroupPlayQuestionState = () => {
  const {game, submitAnswer} = useGameContext();
  const {user, userId} = useSession();
  const {
    questions: allQuestions = EMPTY_ARRAY,
    _id: gameId,
    leaderBoard = EMPTY_ARRAY,
    startTime: gameStartTime,
    config,
  } = game;

  const timePerQuestion = _get(config, 'maxTimePerQuestion', 10);

  const initialValue = _reduce(
    allQuestions,
    (acc, questionObject) => {
      const {submissions, question} = questionObject;
      const submissionByCurrentUser = _filter(
        submissions,
        (submission) => submission?.userId === user?._id,
      );

      const {id} = question;
      acc[id] = {
        question,
        hasSolved: false,
        incorrectAttempts: 0,
      };

      if (!_isEmpty(submissionByCurrentUser)) {
        acc[id].hasSolved = true;
      }
      return acc;
    },
    {},
  );

  const [questions, setQuestions] = useState(initialValue);
  const [questionStartTime, setQuestionStartTime] = useState(
    getCurrentTimeWithOffset(),
  );
  const questionsRef = useRef(questions);
  questionsRef.current = questions;

  const updateQuestion = useCallback(
    ({qid, key, value}) => {
      const prevQuestionState = questions[qid];
      if (_isEmpty(prevQuestionState)) return;

      setQuestions((prevState) => ({
        ...prevState,
        [qid]: {
          ...prevState[qid],
          [key]: value,
        },
      }));
    },
    [questions],
  );

  const getCurrentQuestionId = useCallback(() => {
    const startTime = new Date(gameStartTime).getTime();
    const questionIndex = calculateCurrentQuestionIndex(
      startTime,
      timePerQuestion,
      WAITING_TIME,
    );
    const questionId = _get(allQuestions[questionIndex], ['question', 'id']);

    return questionId;
  }, [gameStartTime, timePerQuestion, allQuestions]);

  const [currentQuestionId, setCurrentQuestionId] = useState(
    getCurrentQuestionId(),
  );

  const [solvedAllQuestions, setSolvedAllQuestions] = useState(false);

  const updateCurrentQuestion = useCallback(() => {
    const firstUnSolvedQuestion = _find(
      _values(questions),
      (questionObject) => !questionObject?.hasSolved,
    );

    if (_isEmpty(firstUnSolvedQuestion)) {
      setSolvedAllQuestions(true);
      return;
    }

    const newQuestionId = getCurrentQuestionId();
    if (newQuestionId !== currentQuestionId) {
      setCurrentQuestionId(newQuestionId);
      setQuestionStartTime(getCurrentTimeWithOffset());
    }
  }, [
    setSolvedAllQuestions,
    questions,
    getCurrentQuestionId,
    currentQuestionId,
  ]);

  const handleCorrectAnswerSubmitted = useCallback(
    ({question, value, isCorrect}) => {
      const {id: questionId} = question;
      updateQuestion({qid: questionId, key: 'hasSolved', value: true});
      const incorrectAttempts = questions[questionId]?.incorrectAttempts ?? 0;

      submitAnswer({
        gameId,
        questionId,
        submittedValue: value,
        timeOfSubmission: getCurrentTimeWithOffset(),
        isCorrect,
        incorrectAttempts,
        userId,
      });
    },
    [gameId, questions, updateQuestion, submitAnswer],
  );

  const handleIncorrectAnswerSubmitted = useCallback(
    ({question, value}) => {
      const {id: questionId} = question;
      const currentAttempts = questions[questionId]?.incorrectAttempts || 0;
      const updatedIncorrectAttempts = currentAttempts + 1;
      updateQuestion({
        qid: questionId,
        key: 'incorrectAttempts',
        value: updatedIncorrectAttempts,
      });
    },
    [questions, updateQuestion],
  );

  const handleSubmitAnswer = useCallback(
    ({questionId, value}) => {
      const questionObj = _find(
        _values(questions),
        (questionObj) => questionObj?.question?.id === questionId,
      );
      const {question} = questionObj ?? EMPTY_OBJECT;
      if (_isEmpty(question)) {
        return false;
      }
      const {answers} = question;
      const isCorrect = _isEqual(answers?.[0], _toString(value));

      if (isCorrect) {
        handleCorrectAnswerSubmitted({
          question,
          value,
          isCorrect,
        });
      } else {
        handleIncorrectAnswerSubmitted({
          question,
          value,
        });
      }

      return isCorrect;
    },
    [questions, handleCorrectAnswerSubmitted, handleIncorrectAnswerSubmitted],
  );

  const playersScores = useMemo(() => {
    const scores = {};
    const currentPlayersLocalScore = _reduce(
      questions,
      (acc, questionObject) => {
        const {hasSolved} = questionObject;
        return acc + (hasSolved ? 1 : 0);
      },
      0,
    );

    for (let i = 0; i < leaderBoard?.length; i++) {
      scores[leaderBoard?.[i]?.userId] = leaderBoard?.[i]?.totalPoints;
    }

    scores[userId] = Math.max(currentPlayersLocalScore, scores[userId]);
    return scores;
  }, [leaderBoard, questions]);

  const handleForceQuestionSubmission = useCallback(
    (originalQuestionId) => {
      const questionObj = questions[currentQuestionId]?.question;
      const hasSolved = _get(questionObj, 'hasSolved', false);
      if (!hasSolved) {
        if (currentQuestionId === originalQuestionId) {
          handleSubmitAnswer({
            questionId: originalQuestionId,
            value: '',
          });
          updateCurrentQuestion();
        }
      }
    },
    [currentQuestionId, handleSubmitAnswer, updateCurrentQuestion, questions],
  );

  useEffect(() => {
    const timer = setInterval(() => {
      updateCurrentQuestion();
    }, 1000);

    return () => clearInterval(timer);
  }, [updateCurrentQuestion]);

  useEffect(() => {
    if (_isEmpty(questionsRef.current) && !_isEmpty(initialValue)) {
      setQuestions(initialValue);
    }

    const timeIntoQuestion =
      (getCurrentTimeWithOffset() - gameStartTime) %
      ((timePerQuestion + WAITING_TIME) * 1000);
    setQuestionStartTime(getCurrentTimeWithOffset() - timeIntoQuestion);
  }, [initialValue, gameStartTime, timePerQuestion]);

  const currentQuestionIndex = useMemo(
    () => {
      return _findIndex(allQuestions, ({question}) =>
        _isEqual(question?.id, currentQuestionId),
      )
    },
    [currentQuestionId, allQuestions],
  );

  const allQuestionsCount = _toNumber(config.timeLimit) / (_toNumber(config.maxTimePerQuestion) + EACH_QUESTION_RESULT_TIME);

  return {
    currentQuestion: questions[currentQuestionId]?.question,
    currentQuestionId,
    submitAnswer: handleSubmitAnswer,
    playersScores,
    handleForceQuestionSubmission,
    incorrectAttempts: questions[currentQuestionId]?.incorrectAttempts,
    solvedAllQuestions,
    questionStartTime,

    currentQuestionIndex,
    allQuestionsCount,
  };
};

export default useGroupPlayQuestionState;
