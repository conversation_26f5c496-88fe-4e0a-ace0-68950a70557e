import { useMutation, gql } from '@apollo/client'
import { useCallback } from 'react'
import _toString from 'lodash/toString'

const REJECT_REMATCH_MUTATION = gql`
  mutation RejectRematch($gameId: ID!) {
    rejectRematch(gameId: $gameId)
  }
`

const useRejectRematchRequest = () => {
  const [rejectRematchQuery] = useMutation(REJECT_REMATCH_MUTATION)

  const rejectRematchRequest = useCallback(({ gameId }) => {
    return rejectRematchQuery({
      variables: {
        gameId: _toString(gameId),
      },
    })
  }, [])

  return {
    rejectRematchRequest,
  }
}

export default useRejectRematchRequest
