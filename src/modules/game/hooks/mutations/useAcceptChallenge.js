import { useMutation, gql } from "@apollo/client";
import { useCallback } from "react";
import _toString from "lodash/toString";
import {GAME_FRAGMENT} from "../../../../core/graphql/fragments/game";

const ACCEPT_CHALLENGE_MUTATION = gql`
    ${GAME_FRAGMENT}
    mutation AcceptRematch($gameId: ID!) {
      acceptChallenge(gameId: $gameId) {
          ...CoreGameFields
      }
    }   
`;

const useAcceptChallengeRequest = () => {
    const [acceptChallengeQuery] = useMutation(ACCEPT_CHALLENGE_MUTATION);

    const acceptChallengeRequest = useCallback(({ gameId }) => {
        return acceptChallengeQuery({
            variables: {
                gameId: _toString(gameId)
            }
        });
    }, [acceptChallengeQuery]);

    return {
        acceptChallengeRequest
    }
}

export default useAcceptChallengeRequest;