import { useMutation, gql } from "@apollo/client";
import {useCallback} from "react";
import _toString from "lodash/toString";

const REQUEST_REMATCH_MUTATION = gql`
    mutation RequestRematch($gameId: ID!) {
        requestRematch(gameId: $gameId)
    }
`;

const useRequestRematch = ({gameId}) => {
    const [requestRematchQuery] = useMutation(REQUEST_REMATCH_MUTATION);

    const requestRematch = useCallback(() => {
        return requestRematchQuery({ variables: {
                gameId: _toString(gameId)
            }});
    }, [gameId]);

    return {
        requestRematch
    }
}

export default useRequestRematch;