import { useMutation, gql } from "@apollo/client";
import { useCallback } from "react";
import _toString from "lodash/toString";
import {GAME_FRAGMENT} from "../../../../core/graphql/fragments/game";

const REJECT_CHALLENGE_REQ_MUTATION = gql`
    mutation RejectChallenge($gameId: ID!) {
        rejectChallenge(gameId: $gameId)
    }
`;

const useRejectChallengeRequest = () => {
    const [rejectChallengeRequestQuery] = useMutation(REJECT_CHALLENGE_REQ_MUTATION);

    const rejectChallengeRequest = useCallback(({ gameId }) => {
        return rejectChallengeRequestQuery({
            variables: {
                gameId: _toString(gameId)
            }
        });
    }, [rejectChallengeRequestQuery]);

    return {
        rejectChallengeRequest
    }
}

export default useRejectChallengeRequest;