import { gql, useMutation } from "@apollo/client";
import { useCallback, useState } from "react";
import _isEmpty from "lodash/isEmpty"
import _isNil from "lodash/isNil"

const LEAVE_GAME_MUTATION = gql`
mutation LeaveGame($gameId: ID!) {
  leaveGame(gameId: $gameId) {
    _id
  }
}
`

const useLeaveGame = () => {
    const [leaveGameQuery] = useMutation(LEAVE_GAME_MUTATION)
    const [leavingGame, setIsLeavingGame] = useState(false)

    const leaveGame = useCallback(async ({ gameId }) => {
        if (leavingGame) {
            return false
        }
        try {
            setIsLeavingGame(true)
            const responseOfRemovePlayer = await leaveGameQuery({
                variables: {
                    gameId: gameId,
                }
            })
            const { data } = responseOfRemovePlayer ?? EMPTY_OBJECT

            setIsLeavingGame(false)
            const isLeftGame = !_isEmpty(data?.leaveGame) && !_isNil(data?.leaveGame)

            return isLeftGame
        } catch (e) {
            setIsLeavingGame(false)
            return false
        } finally {
            setIsLeavingGame(false)
        }
    }, [leaveGameQuery, leavingGame])

    return {
        leaveGame,
        leavingGame
    }
}

export default useLeaveGame