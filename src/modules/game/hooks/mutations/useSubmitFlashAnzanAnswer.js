import { useMutation, gql } from "@apollo/client";
import { useCallback } from "react";
import _toString from "lodash/toString";
import { GAME_FRAGMENT } from "../../../../core/graphql/fragments/game";

const SUBMIT_FLASH_ANZAN_ANSWER = gql`
    ${GAME_FRAGMENT}
    mutation SubmitFlashAnzanAnswer($answerInput: SubmitFlashAnzanAnswerInput) {
        submitFlashAnzanAnswer(answerInput: $answerInput) {
          ...CoreGameFields
      }
}
`;

const useSubmitFlashAnzanAnswer = () => {
    const [submitFlashAnzanAnswerQuery] = useMutation(SUBMIT_FLASH_ANZAN_ANSWER);

    const submitFlashAnzanAnswer = useCallback((answerInput) => {
        return submitFlashAnzanAnswerQuery({
            variables: {
                answerInput: {...answerInput}
            }
        });
    }, [submitFlashAnzanAnswerQuery]);

    return {
        submitFlashAnzanAnswer
    }
}

export default useSubmitFlashAnzanAnswer;