/* eslint-disable import/no-unused-modules */
/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useMemo, useState } from 'react';
import _isEmpty from 'lodash/isEmpty';
import _compact from 'lodash/compact';
import userReader from 'core/readers/userReader';
import { WEBSOCKET_CHANNELS } from '@/src/core/constants/websocket';
import useWebsocketStore from 'store/useWebSocketStore';
import { useSession } from '../../auth/containers/AuthProvider';

export const useGroupPlayChatEventSubscription = ({ gameId }) => {
  const {
    isConnected,
    lastMessage,
    sendMessage: publishMessage,
    joinChannel,
    leaveChannel,
  } = useWebsocketStore((state) => ({
    isConnected: state.isConnected,
    lastMessage: state.lastMessage,
    sendMessage: state.sendMessage,
    joinChannel: state.joinChannel,
    leaveChannel: state.leaveChannel,
  }));

  const [messages, setMessages] = useState([]);

  const { user, userId } = useSession();
  const channel = WEBSOCKET_CHANNELS.GroupChatEvents(gameId);

  useEffect(() => {
    if (isConnected) {
      joinChannel(channel);
    }
    return () => {
      leaveChannel(channel);
    };
  }, [isConnected]);

  const sendMessage = useCallback(
    ({ message }) => {
      publishMessage({
        type: 'sendMessage',
        channel,
        data: {
          gameId,
          message,
          userName: userReader.username(user),
          userId,
        },
      });
    },
    [gameId, user, userId],
  );

  const message = useMemo(() => {
    const data = lastMessage?.[channel];
    return data;
  }, [lastMessage?.[channel]]);

  useEffect(() => {
    if (!_isEmpty(message)) {
      setMessages((prev) => _compact([...prev, message]));
    }
  }, [message]);

  return {
    messages,
    sendMessage,
  };
};

export default useGroupPlayChatEventSubscription;
