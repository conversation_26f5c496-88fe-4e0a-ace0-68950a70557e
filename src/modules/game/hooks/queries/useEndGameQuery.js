import { gql, useMutation } from '@apollo/client'

import { GAME_FRAGMENT } from '@/src/core/graphql/fragments/game'
import { useCallback } from 'react'

const END_GAME_QUERY = gql`
    ${GAME_FRAGMENT}
    mutation EndGame($gameId: ID) {
        endGame(gameId: $gameId) {
            ...CoreGameFields
        }
    }
`

const useEndGameQuery = () => {
    const [endGameQuery, { loading }] = useMutation(END_GAME_QUERY)

    const endGame = useCallback(
        ({ gameId }) => {
            return endGameQuery({ variables: { gameId } })
        },
        [endGameQuery]
    )

    return {
        endGame,
        endingGame: loading,
    }
}

export default useEndGameQuery
