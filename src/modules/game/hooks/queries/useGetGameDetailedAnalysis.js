import { gql, useQuery } from "@apollo/client"

const GET_GAME_DETAILED_ANALYSIS_QUERY = gql`
query GetGameDetailedAnalysis($gameId: ID) {
  getGameDetailedAnalysis(gameId: $gameId) {
    game {
      _id
      players {
        userId
        rating
        status
      }
      gameStatus
      rematchRequestedBy
      gameType
      createdBy
      config {
        timeLimit
        numPlayers
      }
    }
    questions {
      question {
        presetIdentifier
        expression
      }
      avgTimes {
        userId
        questionAvgTime
        presetAvgTime
        presetBestTime
      }
      globalAvgTime
      globalBestTime
    }
  }
}`


const useGetGameDetailedAnalysis = ({ gameId }) => {
    const { data, loading, error } = useQuery(GET_GAME_DETAILED_ANALYSIS_QUERY, {
        fetchPolicy: 'cache-first',
        variables: {
            gameId
        }
    })
    const { getGameDetailedAnalysis } = data ?? EMPTY_OBJECT

    return {
        gameAnalysis: getGameDetailedAnalysis,
        loading,
        error
    }
}

export default useGetGameDetailedAnalysis