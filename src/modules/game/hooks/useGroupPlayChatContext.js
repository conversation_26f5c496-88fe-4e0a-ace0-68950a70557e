import React, { useContext, useEffect } from 'react';
import _values from 'lodash/values';
import _reduce from 'lodash/reduce';
import _isNil from 'lodash/isNil';
import _isEqual from 'lodash/isEqual';
import useGroupPlayChatEventSubscription from './useGroupPlayChatEventSubscription';
import GroupChatContext, { GroupChatContextProvider } from '../context/groupChatContext';

const WithGroupPlayChatContext = (Component) => {
    const GroupChatContextWrapper = (props) => {
        const { gameId } = props ?? EMPTY_OBJECT
        
        const contextValue = useGroupPlayChatEventSubscription({ gameId });

        return (
            <GroupChatContextProvider value={contextValue}>
                <Component {...props} />
            </GroupChatContextProvider>
        );
    };

    return React.memo(GroupChatContextWrapper);
};

export const useGroupPlayChatContext = () => {
    const contextValue = useContext(GroupChatContext);
    return contextValue;
};


export default WithGroupPlayChatContext
