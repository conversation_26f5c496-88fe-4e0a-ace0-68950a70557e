import _reduce from 'lodash/reduce';

import useFetchUserByUserIdQuery from 'core/hooks/useFetchUserByUserIdQuery';
import { useSession } from '../../auth/containers/AuthProvider';
import { useCallback, useEffect, useMemo, useState } from 'react';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _pick from 'lodash/pick';
import _size from 'lodash/size';

const useGamePlayers = ({ game = EMPTY_OBJECT }) => {
  const { players } = game;

  const playersSize = _size(players);

  const { user: currentUser, updateCurrentUser } = useSession();
  const { fetchUserById } = useFetchUserByUserIdQuery();

  const [resolvedPlayers, setResolvedPlayers] = useState({
    [currentUser?._id]: currentUser,
  });

  const playerIds = useMemo(() => {
    if (_isEmpty(players)) return EMPTY_ARRAY;

    const _playerIds = _map(players, 'userId');
    return _playerIds.sort((a, b) => (a === currentUser?._id ? -1 : 1));
  }, [currentUser?._id, players, playersSize]);

  const userIdsToFetch = useMemo(
    () =>
      _reduce(
        players,
        (acc, player) => {
          const { userId } = player;
          if (!_isEmpty(resolvedPlayers[userId])) {
            return acc;
          }
          acc.push(userId);
          return acc;
        },
        [],
      ),
    [resolvedPlayers, players],
  );

  const fetchAndUpdateUsers = useCallback(
    ({ userIds, queryOptions }) => {
      const promises = _map(userIds, (userId) =>
        fetchUserById({ userId, queryOptions }),
      );
      Promise.all(promises).then((responses) => {
        const usersMap = _reduce(
          responses,
          (acc, response) => {
            const resolvedUser = response?.data?.user;
            acc[resolvedUser?._id] = resolvedUser;

            if (resolvedUser?._id === currentUser?._id) {
              updateCurrentUser(resolvedUser);
            }
            return acc;
          },
          {},
        );
        setResolvedPlayers((prevResolvedPlayers) =>
          _pick(
            {
              ...prevResolvedPlayers,
              ...usersMap,
            },
            playerIds,
          ),
        );
      });
    },
    [setResolvedPlayers, fetchUserById, currentUser, playerIds],
  );

  const resolveUsers = useCallback(() => {
    fetchAndUpdateUsers({ userIds: userIdsToFetch });
  }, [fetchAndUpdateUsers, userIdsToFetch]);

  const refreshAllPlayers = useCallback(() => {
    const userIds = _map(players, 'userId');
    fetchAndUpdateUsers({
      userIds,
      queryOptions: { fetchPolicy: 'network-only' },
    });
  }, [players, fetchAndUpdateUsers]);

  // const resolveUsersRef = useRef(resolveUsers);

  useEffect(() => {
    resolveUsers();
  }, [playersSize]);

  return {
    players: resolvedPlayers,
    refreshAllPlayers,
  };
};

export default useGamePlayers;
