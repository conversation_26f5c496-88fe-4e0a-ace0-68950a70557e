import React from 'react';
import { View } from 'react-native';
import PropTypes from 'prop-types';
import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import dark from 'core/constants/themes/dark';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { useFeatureValue } from '@growthbook/growthbook-react';
import FEATURE_FLAGS from 'core/constants/featureFlags';
import useGameResultFooterStyles from './GameResultFooter.style';

const GameResultFooter = ({ rematchWithSamePlayer, navigateToNewGame }) => {
  const styles = useGameResultFooterStyles();
  const { isMobile } = useMediaQuery();

  const swapOrder = useFeatureValue(
    FEATURE_FLAGS.SWAP_GAME_RESULT_FOOTER_ORDER,
    false,
  );

  return (
    <View style={[styles.footer, !isMobile && { height: 40 }]}>
      <View
        style={[
          styles.footerContainer,
          swapOrder && { flexDirection: 'row-reverse' },
        ]}
      >
        <InteractivePrimaryButton
          label="REMATCH"
          labelStyle={styles.bottomButtonLabel}
          buttonStyle={[
            styles.bottomButtonStyle,
            { borderColor: dark.colors.metricsBackgroundColor },
          ]}
          buttonBorderBackgroundStyle={[
            styles.bottomButtonBackgroundStyle,
            { backgroundColor: dark.colors.metricsBackgroundColor },
          ]}
          onPress={rematchWithSamePlayer}
        />
        <InteractivePrimaryButton
          label="NEW GAME"
          labelStyle={styles.bottomButtonLabel}
          buttonStyle={styles.bottomButtonStyle}
          buttonBorderBackgroundStyle={styles.bottomButtonBackgroundStyle}
          onPress={navigateToNewGame}
        />
      </View>
    </View>
  );
};

GameResultFooter.propTypes = {
  rematchWithSamePlayer: PropTypes.func.isRequired,
  navigateToNewGame: PropTypes.func.isRequired,
};

export default React.memo(GameResultFooter);
