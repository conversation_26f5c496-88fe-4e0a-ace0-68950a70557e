import React from 'react';
import _isNil from 'lodash/isNil';

import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import { PUZZLE_GAME_TYPES } from '@/src/modules/home/<USER>/puzzleGameTypes';
import { WithErrorBoundary } from 'atoms/ErrorBoundary';
import CrossMathPuzzleGame from '../CrossMathPuzzleGame';
import usePuzzleGameContext, {
  WithPuzzleGameContext,
} from '../../hooks/usePuzzleGameContext';

const ComponentFactory = {
  [PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_WITH_FRIEND]: CrossMathPuzzleGame,
  [PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_DUEL]: CrossMathPuzzleGame,
};

const PuzzleGame = ({ game }: { game: any }) => {
  const { gameType } = game;

  const Component = ComponentFactory[gameType];

  if (_isNil(Component)) {
    return <ErrorView errorMessage="Invalid game type!" />;
  }

  return <Component game={game} />;
};

const PuzzleGameContainer = () => {
  const { game, gameMeta } = usePuzzleGameContext();

  const { loading } = gameMeta ?? EMPTY_OBJECT;

  if (_isNil(game)) {
    if (loading) {
      return <Loading label="Loading Game" />;
    }
    return <ErrorView errorMessage="Something went wrong!" />;
  }

  return <PuzzleGame game={game} />;
};

PuzzleGameContainer.displayName = 'PuzzleGameContainer';

export default React.memo(
  WithPuzzleGameContext(WithErrorBoundary(PuzzleGameContainer)),
);
