import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import { useRouter } from 'expo-router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import contestReader from 'core/readers/contestReader';
import { getContestPropertiesToTrack } from '../utils/contestEvents';
import useContestRegistration from './useContestRegistration';
import { useSession } from '../../auth/containers/AuthProvider';

const FIVE_MINUTES_IN_MILLI_SECOND = 5 * 60 * 1000;

interface useContestDetailsControllerProps {
  contest: any;
  onRegisterRequest?: () => void;
  refetch?: () => void;
  onContestRegistrationSuccess?: () => void;
  onContestRegistrationFailure?: () => void;
}

const useContestDetailsController = ({
  contest,
  onRegisterRequest,
  refetch,
  onContestRegistrationSuccess,
  onContestRegistrationFailure,
}: useContestDetailsControllerProps) => {
  const contestId = contestReader.id(contest);

  const { user } = useSession();
  const { isGuest } = user ?? EMPTY_OBJECT;

  const router = useRouter();
  const { isSubmitting, handleSubmit, onPressUnRegister } =
    useContestRegistration({
      contest,
      refetch,
      onContestRegistrationSuccess,
      onContestRegistrationFailure,
    });

  const startTime = contestReader.startTime(contest);
  const endTime = contestReader.endTime(contest);
  const formFields = contestReader.formFields(contest);

  const now = getCurrentTimeWithOffset();
  const [isLive, setIsLive] = useState(
    now >= new Date(startTime).getTime() && now <= new Date(endTime).getTime(),
  );
  const [isAboutToStart, setIsAboutToStart] = useState(false);
  const endTimeStamp = new Date(endTime).getTime();
  const [hasEnded, setHasEnded] = useState(now >= endTimeStamp);
  const refetchRef = useRef(refetch);
  refetchRef.current = refetch;

  const hasUserRegistered = !_isNil(contest?.currentUserParticipation);

  const userRegisteredAndContestIsLive = hasUserRegistered && isLive;
  const userIsNotRegisteredAndContestIsNotLive =
    !hasUserRegistered && !hasEnded;

  const handleJoinOrSubmitButtonPress = useCallback(() => {
    if (isSubmitting) {
      return;
    }
    if (userRegisteredAndContestIsLive) {
      Analytics.track(ANALYTICS_EVENTS.CONTEST.CLICKED_ON_JOIN_NOW, {
        ...getContestPropertiesToTrack({ contest }),
        [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_DETAILS,
      });
      router.push(`contest/play/${contestId}`);
      return;
    }
    if (userIsNotRegisteredAndContestIsNotLive) {
      Analytics.track(ANALYTICS_EVENTS.CONTEST.CLICKED_ON_JOIN_NOW, {
        ...getContestPropertiesToTrack({ contest }),
        [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_DETAILS,
      });
      if (_isEmpty(formFields)) {
        handleSubmit?.(EMPTY_ARRAY);
        return;
      }
      if (!_isEmpty(formFields)) {
        Analytics.track(
          ANALYTICS_EVENTS.CONTEST.VIEWED_REGISTRATION_BOTTOM_SHEET,
          {
            ...getContestPropertiesToTrack({ contest }),
            [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_DETAILS,
          },
        );
      }
      onRegisterRequest?.();

      return;
    }
    router.push(`contest/play/${contestId}`);
  }, [
    userRegisteredAndContestIsLive,
    userIsNotRegisteredAndContestIsNotLive,
    contest,
    contestId,
    isSubmitting,
  ]);

  const onPressRegisterGuestUser = useCallback(() => {
    showToast({
      type: TOAST_TYPE.ERROR,
      description:
        'Guest Users or Users with less than 3 games played are not allowed to participate in Contest!',
    });
  }, []);

  const onPressViewLeaderBoard = useCallback(() => {
    router.push(`contest/leaderboard/${contestId}`);
  }, [contestId]);

  useEffect(() => {
    const currentTime = getCurrentTimeWithOffset();
    const startDateTime = new Date(startTime).getTime();
    const endDateTime = new Date(endTime).getTime();

    setIsLive(currentTime >= startDateTime && currentTime <= endDateTime);
    setHasEnded(currentTime >= endTimeStamp);

    if (startDateTime > currentTime) {
      const timeUntilStart = startDateTime - currentTime;
      setTimeout(() => {
        setIsLive(true);
        refetchRef.current?.();
      }, timeUntilStart);
    }

    if (endDateTime > currentTime) {
      const timeUntilEnd = endDateTime - currentTime;
      setTimeout(() => {
        setIsLive(false);
        setHasEnded(true);
      }, timeUntilEnd);
    }

    const interval = setInterval(() => {
      const currentTime = getCurrentTimeWithOffset();
      if (startDateTime > currentTime) {
        const timeUntilStart = startDateTime - currentTime;
        if (timeUntilStart <= FIVE_MINUTES_IN_MILLI_SECOND) {
          setIsAboutToStart(true);
        }
        if (timeUntilStart <= 0) {
          setIsAboutToStart(false);
          clearInterval(interval);
        }
      } else {
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime, endTime]);

  // @Ritesh Make it Dynamic
  const hasCompleted = false;

  return {
    handleJoinOrSubmitButtonPress,
    handleFormSubmit: handleSubmit,
    onPressViewLeaderBoard,
    onPressUnRegister,
    onPressRegisterGuestUser,
    isAboutToStart,
    isLive,
    isGuest,
    hasCompleted,
    hasEnded,
    hasUserRegistered,
    formFields,
    isSubmitting,
    startTime,
    endTime,
  };
};

export default useContestDetailsController;
