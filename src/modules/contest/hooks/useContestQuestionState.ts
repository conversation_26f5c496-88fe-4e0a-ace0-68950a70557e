import _reduce from 'lodash/reduce';
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import _size from 'lodash/size';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import _head from 'lodash/head';
import _keys from 'lodash/keys';
import _find from 'lodash/find';
import _values from 'lodash/values';
import _toString from 'lodash/toString';
import _get from 'lodash/get';
import useSubmitContestAnswer from './mutations/useSubmitContestAnswer';
import useSubmitVirtualContestAnswer from './mutations/useSubmitVirtualContestAnswer';

interface useContestQuestionStateProps {
  contest: any;
  initialUserSubmission: any;
  isVirtualContest?: boolean;
}

const useContestQuestionState = ({
  contest,
  initialUserSubmission,
  isVirtualContest = false,
}: useContestQuestionStateProps) => {
  const { questions: initialQuestions, _id: contestId } = contest;

  const { submitContestAnswer: submitContestAnswerInLiveContest } =
    useSubmitContestAnswer();

  const { submitVirtualContestAnswer } = useSubmitVirtualContestAnswer();

  const submitContestAnswer = useMemo(() => {
    if (!isVirtualContest) {
      return submitContestAnswerInLiveContest;
    }
    return submitVirtualContestAnswer;
  }, [
    submitVirtualContestAnswer,
    submitContestAnswerInLiveContest,
    isVirtualContest,
  ]);

  const normalizedInitialSubmissions = _reduce(
    initialUserSubmission?.submissions,
    (acc: any, submission) => {
      const { questionId } = submission;
      acc[questionId] = submission;
      return acc;
    },
    {},
  );

  const initialSubmittedAnswers: any = [];

  const initialValue = _reduce(
    initialQuestions,
    (acc: any, questionObject) => {
      const { question, points } = questionObject;

      const { id } = question;
      const submission = normalizedInitialSubmissions[id];

      if (submission?.isCorrect) {
        initialSubmittedAnswers.push(id);
      }

      acc[id] = {
        question,
        points,
        hasSolved: submission?.isCorrect ?? false,
      };
      return acc;
    },
    {},
  );

  const [submittedAnswersIds, setSubmittedAnswersIds] = useState(
    initialSubmittedAnswers,
  );
  const [questions, setQuestions] = useState(initialValue);

  const currentScore = useMemo(
    () =>
      _reduce(questions, (acc, { hasSolved }) => acc + (hasSolved ? 1 : 0), 0),
    [questions],
  );

  const updateQuestion = useCallback(
    ({ qid, value }: { qid: string; value: any }) => {
      const prevQuestionState = questions[qid];
      if (_isEmpty(prevQuestionState)) return;

      setQuestions((prevState: any) => ({
        ...prevState,
        [qid]: {
          ...prevState[qid],
          ...value,
        },
      }));
    },
    [setQuestions, questions],
  );

  const [currentQuestionId, setCurrentQuestionId] = useState(
    _head(_keys(questions)),
  );

  const [solvedAllQuestions, setSolvedAllQuestions] = useState(false);

  const updateCurrentQuestion = useCallback(() => {
    const firstUnSolvedQuestion = _find(
      _values(questions),
      (questionObject) => !questionObject?.hasSolved,
    );

    if (_isEmpty(firstUnSolvedQuestion)) {
      setSolvedAllQuestions(true);
      return;
    }
    const { question } = firstUnSolvedQuestion;
    setCurrentQuestionId(question?.id);
  }, [questions]);

  const submitAnswer = useCallback(
    ({
      questionId,
      value,
      timeTaken,
    }: {
      questionId: string;
      value: string;
      timeTaken: number;
    }) => {
      const { question: originalQuestion } = questions[questionId];
      const { answers } = originalQuestion;
      if (
        _size(answers) > 0 &&
        _isEqual(_toString(answers[0]), _toString(value))
      ) {
        const hasNowSolvedAllQuestions =
          _size(initialQuestions) === _size(submittedAnswersIds) + 1;
        setSolvedAllQuestions(hasNowSolvedAllQuestions);
        updateQuestion({
          qid: questionId,
          value: {
            hasSolved: true,
            timeTaken,
          },
        });
        submitContestAnswer({
          questionId,
          contestId,
          answer: value,
        }).then((res) => {
          const submittedContestAnswer = _get(res, [
            'data',
            'submitContestAnswer',
          ]);
          if (submittedContestAnswer) {
            setSubmittedAnswersIds((prevIds: any) => [...prevIds, questionId]);
          }
        });
      }
    },
    [
      questions,
      initialQuestions,
      submittedAnswersIds,
      updateQuestion,
      submitContestAnswer,
      contestId,
    ],
  );

  const totalTimeTaken = useMemo(
    () => _reduce(questions, (acc, { timeTaken = 0 }) => acc + timeTaken, 0),
    [questions],
  );

  const updateCurrentQuestionRef = useRef(updateCurrentQuestion);
  updateCurrentQuestionRef.current = updateCurrentQuestion;

  useEffect(() => {
    updateCurrentQuestionRef.current();
  }, [questions]);

  return {
    currentQuestionId,
    submitAnswer,
    currentScore,
    currentQuestion: questions[currentQuestionId]?.question,
    solvedAllQuestions,
    submittedAllAnswersSuccessFully:
      _size(submittedAnswersIds) === _size(initialQuestions),
    totalTimeTaken,
  };
};

export default useContestQuestionState;
