import { gql, useQuery } from '@apollo/client';
import { CONTEST_FRAGMENT } from 'core/graphql/fragments/contest';

const FEATURED_CONTESTS_QUERY = gql`
  ${CONTEST_FRAGMENT}
  query FeaturedContestQuery {
    getFeaturedContests {
      ...ContestFields
    }
  }
`;

const useFetchFeaturedContests = () => {
  const { data, loading, error, refetch } = useQuery(FEATURED_CONTESTS_QUERY, {
    fetchPolicy: 'cache-first',
  });

  return {
    featuredContests: data?.getFeaturedContests || [],
    loading,
    error,
    refetch: refetch,
  };
};

export default useFetchFeaturedContests;
