import _isNil from 'lodash/isNil';
import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import _get from 'lodash/get';

const JOIN_VIRTUAL_CONETST_MUTATION = gql`
  mutation JoinVirtualContest($contestId: ID!) {
    joinVirtualContest(contestId: $contestId)
  }
`;

const useJoinVirtualContest = () => {
  const [joinVirtualContestQuery, { loading }] = useMutation(
    JOIN_VIRTUAL_CONETST_MUTATION,
  );

  const joinVirtualContest = useCallback(
    async ({ contestId }: { contestId: string }) => {
      if (loading) {
        return false;
      }

      if (_isNil(contestId)) {
        return false;
      }

      try {
        const responseOfJoinVirtualContest = await joinVirtualContestQuery({
          variables: {
            contestId,
          },
        });

        return _get(
          responseOfJoinVirtualContest,
          ['data', 'joinVirtualContest'],
          false,
        );
      } catch (e) {
        return false;
      }
    },
    [joinVirtualContestQuery],
  );

  return {
    joinVirtualContest,
  };
};

export default useJoinVirtualContest;
