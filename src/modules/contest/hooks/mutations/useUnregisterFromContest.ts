import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';

const UNREGISTER_FROM_CONTEST = gql`
  mutation UnRegisterForContest($contestId: ID!) {
    unregisterFromContest(contestId: $contestId)
  }
`;

const useUnRegisterFromContest = () => {
  const [unregisterFromContestQuery, { loading }] = useMutation(
    UNREGISTER_FROM_CONTEST,
  );

  const unregisterFromContest = useCallback(
    ({ contestId }: { contestId: string }) => {
      if (loading) {
        return;
      }
      return unregisterFromContestQuery({ variables: { contestId } });
    },
    [unregisterFromContestQuery],
  );

  return { unregisterFromContest };
};

export default useUnRegisterFromContest;
