import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';

import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';

const REGISTER_FOR_CONTEST_MUTATION = gql`
  mutation RegisterForContest($input: RegistrationFormValuesInput!) {
    registerForContest(input: $input)
  }
`;

const useRegisterForContest = () => {
  const [registerForContestMutation] = useMutation(
    REGISTER_FOR_CONTEST_MUTATION,
  );

  const registerForContest = useCallback(
    ({ contestId, formData }: { contestId: string; formData: any }) => {
      Analytics.track(ANALYTICS_EVENTS.CONTEST.CONTEST_REGISTRATION_SUBMITTED, {
        contestId,
      });
      return registerForContestMutation({
        variables: {
          input: {
            contestId,
            formData,
          },
        },
      });
    },
    [registerForContestMutation],
  );

  return {
    registerForContest,
  };
};

export default useRegisterForContest;
