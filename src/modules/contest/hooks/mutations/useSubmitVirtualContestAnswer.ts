import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';

const SUBMIT_VIRTUAL_CONTEST_ANSWER_QUERY = gql`
  mutation SubmitVirtualContestAnswer(
    $contestId: ID!
    $questionId: String!
    $answer: String!
  ) {
    submitVirtualContestAnswer(
      contestId: $contestId
      questionId: $questionId
      answer: $answer
    )
  }
`;

const useSubmitVirtualContestAnswer = () => {
  const [submitVirtualContestAnswerQuery] = useMutation(
    SUBMIT_VIRTUAL_CONTEST_ANSWER_QUERY,
  );

  const submitVirtualContestAnswer = useCallback(
    ({
      contestId,
      questionId,
      answer,
    }: {
      contestId: string;
      questionId: string;
      answer: string;
    }) => {
      return submitVirtualContestAnswerQuery({
        variables: {
          contestId,
          questionId,
          answer,
        },
      });
    },
    [submitVirtualContestAnswerQuery],
  );

  return {
    submitVirtualContestAnswer,
  };
};

export default useSubmitVirtualContestAnswer;
