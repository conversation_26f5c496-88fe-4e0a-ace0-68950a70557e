import _isEmpty from 'lodash/isEmpty';
import { gql, useQuery } from '@apollo/client';
import { useMemo } from 'react';
import { decryptJsonData } from 'core/utils/encryptions/decrypt';
import _map from 'lodash/map';
import _get from 'lodash/get';
import { CONTEST_FRAGMENT } from 'core/graphql/fragments/contest';

const CONTEST_DETAILS_QUERY = gql`
  ${CONTEST_FRAGMENT}
  query GetContestQuery($contestId: ID!) {
    getContestById(contestId: $contestId) {
      ...ContestFields
    }
  }
`;

const useContestDetails = ({ contestId }: { contestId: string }) => {
  const { data, loading, error, refetch } = useQuery(CONTEST_DETAILS_QUERY, {
    variables: { contestId },
    fetchPolicy: 'cache-and-network',
  });

  const contestDetails = useMemo(() => {
    const contestInfoObj = _get(data, 'getContestById', EMPTY_OBJECT);
    if (_isEmpty(contestInfoObj)) {
      return contestInfoObj;
    }
    const { encryptedQuestions } = contestInfoObj;
    const questions = _map(encryptedQuestions, decryptJsonData);
    return {
      ...contestInfoObj,
      questions,
    };
  }, [data]);

  return {
    loading,
    error,
    refetch,
    contestDetails,
  };
};

export default useContestDetails;
