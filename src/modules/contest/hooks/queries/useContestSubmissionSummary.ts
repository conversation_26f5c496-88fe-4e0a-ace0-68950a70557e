import { gql, useQuery } from '@apollo/client';

const USER_SUBMISSIONS_SUMMERY_FOR_CONTEST = gql`
  query GetUserContestSubmissions($contestId: ID!) {
    getUserContestSubmissions(contestId: $contestId) {
      totalScore
      lastSubmissionTime
      startTime
      correctSubmission
      incorrectSubmission
    }
  }
`;

const useGetContestSubmissionSummary = ({ contestId }) => {
  const { data, error, loading, refetch } = useQuery(
    USER_SUBMISSIONS_SUMMERY_FOR_CONTEST,
    {
      fetchPolicy: 'cache-and-network',
      variables: {
        contestId,
      },
    },
  );

  return {
    error,
    loading,
    refetch,
    userSubmission: data?.getUserContestSubmissions,
  };
};

export default useGetContestSubmissionSummary;
