import { gql, useQuery } from '@apollo/client';

const USER_SUBMISSIONS_FOR_CONTEST = gql`
  query GetUserContestSubmissions($contestId: ID!) {
    getUserContestSubmissions(contestId: $contestId) {
      totalScore
      startTime
      lastSubmissionTime
      correctSubmission
      incorrectSubmission
      submissions {
        questionId
        answer
        isCorrect
        submissionTime
        points
      }
    }
  }
`;

const useGetUserSubmissionInContest = ({
  contestId,
}: {
  contestId: string;
}) => {
  const { data, error, loading, refetch } = useQuery(
    USER_SUBMISSIONS_FOR_CONTEST,
    {
      variables: {
        contestId,
      },
      fetchPolicy: 'cache-and-network',
    },
  );

  return {
    userSubmission: data?.getUserContestSubmissions,
    error,
    loading,
    refetch,
  };
};

export default useGetUserSubmissionInContest;
