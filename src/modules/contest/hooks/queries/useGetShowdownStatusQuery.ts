import { gql, useQuery } from '@apollo/client';
import _includes from 'lodash/includes';

const GET_SHOWDOWNS_BY_STATUS = gql`
  query GetShowdownsByStatus(
    $statuses: [SHOWDOWN_CONTEST_STATUS!]!
    $sortDirection: String
  ) {
    getShowdownsByStatus(statuses: $statuses, sortDirection: $sortDirection) {
      showdowns {
        _id
        name
        description
        startTime
        endTime
        # showdownDuration
        # hostedBy
        registrationStartTime
        registrationEndTime
        registrationCount
        registrationCount
        status
        currentRound
        rounds
        currentUserParticipation {
          userId
          totalScore
          stats {
            currentScore
          }
        }
      }
      count
    }
  }
`;

const useGetShowdownsByStatus = ({ statuses }: { statuses: string[] }) => {
  const { data, error, loading, refetch } = useQuery(GET_SHOWDOWNS_BY_STATUS, {
    variables: {
      statuses,
      sortDirection: _includes(statuses, 'ENDED') ? 'DESC' : 'ASC',
    },
  });

  return {
    showdownDetail: data?.getShowdownsByStatus,
    error,
    loading,
    refetch,
  };
};

export default useGetShowdownsByStatus;
