import { gql, useQuery } from '@apollo/client';

const INDIVIDUAL_PARTICIPANT_SUBMISSIONS_FOR_CONTEST = gql`
  query GetUserContestSubmissions($contestId: ID!, $userId: ID) {
    getUserContestSubmissions(contestId: $contestId, userId: $userId) {
      totalScore
      startTime
      lastSubmissionTime
      correctSubmission
      incorrectSubmission
      submissions {
        questionId
        answer
        isCorrect
        submissionTime
        points
      }
      user {
        _id
        name
        profileImageUrl
        globalRank
      }
    }
  }
`;

const useGetIndividualParticipantScoreInContest = ({
  contestId,
  userId,
}: {
  contestId: string;
  userId: string;
}) => {
  const { data, error, loading, refetch } = useQuery(
    INDIVIDUAL_PARTICIPANT_SUBMISSIONS_FOR_CONTEST,
    {
      variables: {
        contestId,
        userId,
      },
    },
  );

  return {
    userSubmission: data?.getUserContestSubmissions,
    error,
    loading,
    refetch,
  };
};

export default useGetIndividualParticipantScoreInContest;
