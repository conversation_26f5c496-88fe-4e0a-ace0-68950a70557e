import { useCallback } from 'react';
import { gql, useLazyQuery } from '@apollo/client';

import { USER_PUBLIC_DETAIL_FRAGMENT } from 'core/graphql/fragments/userPublicDetail';

const GET_CONTEST_LEADERBOARD_QUERY = gql`
  ${USER_PUBLIC_DETAIL_FRAGMENT}
  query GetContestLeaderboard(
    $contestId: ID!
    $pageNumber: Int
    $pageSize: Int
  ) {
    getContestLeaderboard(
      contestId: $contestId
      pageNumber: $pageNumber
      pageSize: $pageSize
    ) {
      participants {
        user {
          ...UserPublicDetailFields
        }
        rank
        score
        startTime
        lastSubmissionTime
        correctSubmission
        incorrectSubmission
      }
      totalParticipants
    }
  }
`;

const DEFAULT_PAGE_SIZE = 50;

const useGetContestLeaderboard = ({
  contestId,
  isLive = false,
  pageSize = DEFAULT_PAGE_SIZE,
}: {
  contestId: string;
  isLive?: boolean;
  pageSize?: number;
}) => {
  const [fetchLeaderboardQuery, { loading, error }] = useLazyQuery(
    GET_CONTEST_LEADERBOARD_QUERY,
    {
      notifyOnNetworkStatusChange: true,
      fetchPolicy: isLive ? 'network-only' : 'cache-first',
    },
  );

  const fetchLeaderboard = useCallback(
    ({ pageNumber }: { pageNumber: number }) => {
      if (loading) {
        printDebug(
          `useGetContestLeaderboard: Skipping fetch for page ${pageNumber} - already loading`,
        );
        return Promise.resolve(null);
      }

      printDebug(
        `useGetContestLeaderboard: Fetching page ${pageNumber} for contest ${contestId}`,
      );

      return fetchLeaderboardQuery({
        variables: {
          contestId,
          pageNumber,
          pageSize,
        },
      });
    },
    [contestId, fetchLeaderboardQuery, loading, pageSize],
  );

  return {
    loading,
    error,
    fetchLeaderboard,
  };
};

export default useGetContestLeaderboard;
