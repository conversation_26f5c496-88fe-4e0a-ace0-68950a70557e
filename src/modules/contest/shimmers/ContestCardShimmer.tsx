import React, { useEffect } from 'react';
import { View, Animated, Dimensions } from 'react-native';
import useContestCardShimmerStyles from './ContestCardShimmer.style';
import LinearGradient from '../../../components/atoms/LinearGradient';

const ContestCardShimmer = () => {
  const { width } = Dimensions.get('window');
  const styles=useContestCardShimmerStyles()
  const animatedValue = new Animated.Value(0);

  useEffect(() => {
    const animation =Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      })
    ).start();
    return () => {
      animatedValue?.setValue(0);
      animatedValue?.stopAnimation();
      animation?.stop();
    };
  }, []);

  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-width, width],
  });

  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <View style={styles.badge} />
        <View style={styles.iconContainer} />
        <View style={styles.title} />
        <View style={styles.dateTime} />
      </View>
      <Animated.View
        style={[
          styles.shimmerOverlay,
          {
            transform: [{ translateX }],
          },
        ]}
      >
        <LinearGradient
          colors={['rgba(255, 255, 255, 0)', 'rgba(255, 255, 255, 0.3)', 'rgba(255, 255, 255, 0)']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{ flex: 1 }}
        />
      </Animated.View>
    </View>
  );
};

export default ContestCardShimmer;
