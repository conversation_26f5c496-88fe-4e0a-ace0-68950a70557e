import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      width: isCompactMode ? 160 : 250,
      height: isCompactMode ? 170 : 240,
      borderRadius: 16,
      padding: 16,
      // marginTop: 30,
      marginRight: 16,
      marginLeft: 16,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: 'rgba(215, 198, 253, 0.3)',
    },
    contentContainer: {
      position: 'relative',
      alignContent: 'center',
      alignItems: 'center',
      zIndex: 1,
      flex: 1,
    },
    badge: {
      position: 'absolute',
      top: 0,
      left: -30,
      width: '50%',
      height: 24,
      borderRadius: 12,
      backgroundColor: 'rgba(215, 198, 253, 0.3)',
    },
    iconContainer: {
      width: isCompactMode ? 60 : 70,
      height: isCompactMode ? 60 : 70,
      marginTop: isCompactMode ? 30 : 40,
      alignItems: 'center',
      borderRadius: 10,
      backgroundColor: 'rgba(215, 198, 253, 0.3)',
      marginBottom: isCompactMode ? 5 : 16,
    },
    title: {
      height: isCompactMode ? 15 : 20,
      marginTop: isCompactMode ? 5 : 15,
      width: '80%',
      backgroundColor: 'rgba(215, 198, 253, 0.3)',
      borderRadius: 4,
      marginBottom: 8,
    },
    dateTime: {
      height: isCompactMode ? 10 : 14,
      width: '60%',
      backgroundColor: 'rgba(215, 198, 253, 0.3)',
      borderRadius: 4,
    },
    shimmerOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 2,
    },
  });

const useContestCardShimmerStyles = () => {
  const { isMobile } = useMediaQuery();

  return useMemo(() => createStyles(isMobile), [isMobile]);
};

export default useContestCardShimmerStyles;
