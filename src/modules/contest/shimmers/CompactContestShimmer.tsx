import React from 'react';
import { ScrollView } from 'react-native';
import Loading from '@/src/components/atoms/Loading';
import styles from '../components/CompactContestDetail/CompactContestDetail.style';

const CompactContestDetailsShimmer = () => (
  <ScrollView
    contentContainerStyle={styles.container}
    showsVerticalScrollIndicator={false}
    showsHorizontalScrollIndicator={false}
  >
    <Loading label="Loading Contest Details" />
  </ScrollView>
);

export default React.memo(CompactContestDetailsShimmer);
