import React, { useEffect } from 'react';
import { Animated, Dimensions, View } from 'react-native';
import styles from './ContestDetailCardShimmer.style';
import LinearGradient from 'atoms/LinearGradient';

const ContestDetailCardShimmer = () => {
  const { width } = Dimensions.get('window');
  const animatedValue = new Animated.Value(0);

  useEffect(() => {
    Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      }),
    ).start();
  }, []);

  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-width, width],
  });

  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <View style={styles.header}>
          <View style={styles.gradientBox}>
            {/* <View style={styles.iconContainer} /> */}
          </View>
          <View style={styles.titleContainer}>
            <View style={styles.title} />
            <View style={styles.hostDetail}>
              <View style={styles.hostedBy} />
              <View style={styles.hostName} />
            </View>
          </View>
        </View>
        <View style={styles.infoRow}>
          <View style={styles.infoBox}>
            <View style={styles.infoTitle} />
            <View style={styles.infoDetails} />
          </View>
          <View style={styles.infoBox}>
            <View style={styles.infoTitle} />
            <View style={styles.infoDetails} />
          </View>
          <View style={styles.infoBox}>
            <View style={styles.infoTitle} />
            <View style={styles.infoDetails} />
          </View>
          <View style={styles.buttonBox}>
            <View style={styles.registerButton} />
            <View style={styles.registerNowDetail}>
              <View style={styles.registrationEnds} />
              <View style={styles.daysLeftDetails} />
            </View>
          </View>
        </View>
      </View>
      <Animated.View
        style={[
          styles.shimmerOverlay,
          {
            transform: [{ translateX }],
          },
        ]}
      >
        <LinearGradient
          colors={[
            'rgba(255, 255, 255, 0)',
            'rgba(255, 255, 255, 0.3)',
            'rgba(255, 255, 255, 0)',
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{ flex: 1 }}
        />
      </Animated.View>
    </View>
  );
};

export default ContestDetailCardShimmer;
