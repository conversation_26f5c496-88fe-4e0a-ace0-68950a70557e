import { StyleSheet } from 'react-native';

export default StyleSheet.create({
  container: {
    marginTop: 30,
    width: '100%',
    overflow: 'hidden',
  },
  tabBarContainer: {
    width: '80%',
    height: 48,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  tabBar: {
    flexDirection: 'row',
    width: '100%',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    height: 48,
  },
  tabLabel: {
    width: '40%',
    height: 14,
    backgroundColor: 'rgba(215, 198, 253, 0.3)',
    borderRadius: 4,
  },
  indicator: {
    position: 'absolute',
    alignItems: 'center',
    bottom: 0,
    left: 50,
    width: '20%',
    height: 2,
    backgroundColor: 'rgba(169, 249, 158, 0.3)',
  },
  contentContainer: {
    padding: 16,
  },
  contentLine: {
    height: 16,
    width: '90%',
    backgroundColor: 'rgba(215, 198, 253, 0.3)',
    borderRadius: 4,
    marginBottom: 8,
  },
  contentLine2: {
    height: 16,
    width: '70%',
    backgroundColor: 'rgba(215, 198, 253, 0.3)',
    borderRadius: 4,
    marginBottom: 8,
  },
  contentLine3: {
    height: 16,
    width: '30%',
    backgroundColor: 'rgba(215, 198, 253, 0.3)',
    borderRadius: 4,
    marginBottom: 8,
  },
  shimmerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});
