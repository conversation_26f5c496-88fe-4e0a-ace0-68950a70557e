import { StyleSheet } from 'react-native';

export default StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    width: '90%',
    overflow: 'hidden',
  },
  contentContainer: {
    position: 'relative',
    zIndex: 1,
  },
  header: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  gradientBox: {
    width: 70,
    height: 70,
    borderRadius: 10,
    backgroundColor: 'rgba(215, 198, 253, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    height: 24,
    width: '80%',
    backgroundColor: 'rgba(215, 198, 253, 0.3)',
    borderRadius: 4,
    marginBottom: 8,
  },
  hostDetail: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hostedBy: {
    height: 16,
    width: 60,
    backgroundColor: 'rgba(215, 198, 253, 0.3)',
    borderRadius: 4,
    marginRight: 8,
  },
  hostName: {
    height: 16,
    width: 100,
    backgroundColor: 'rgba(215, 198, 253, 0.3)',
    borderRadius: 4,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  infoBox: {
    flex: 1,
    marginRight: 8,
  },
  infoTitle: {
    height: 16,
    width: '40%',
    backgroundColor: 'rgba(215, 198, 253, 0.3)',
    borderRadius: 4,
    marginBottom: 4,
  },
  infoDetails: {
    height: 20,
    width: '70%',
    backgroundColor: 'rgba(215, 198, 253, 0.3)',
    borderRadius: 4,
  },
  buttonBox: {
    flex:1,
    alignItems: 'center',
  },
  registerButton: {
    height: 40,
    width: '80%',
    backgroundColor: 'rgba(215, 198, 253, 0.3)',
    borderRadius: 10,
    marginBottom: 8,
  },
  registerNowDetail: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  registrationEnds: {
    height: 16,
    width: 120,
    backgroundColor: 'rgba(215, 198, 253, 0.3)',
    borderRadius: 4,
    marginRight: 8,
  },
  daysLeftDetails: {
    height: 16,
    width: 60,
    backgroundColor: 'rgba(215, 198, 253, 0.3)',
    borderRadius: 4,
  },
  shimmerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2,
  },
});
