import React, { useEffect } from 'react';
import { Animated, Dimensions, View } from 'react-native';
import LinearGradient from 'atoms/LinearGradient';
import useContestBannerShimmerStyles from './ContestBannerShimmer.style';

const UserScoreInContestRowShimmer = () => {
  const { width } = Dimensions.get('window');
  const animatedValue = new Animated.Value(0);
  const styles = useContestBannerShimmerStyles();

  useEffect(() => {
    Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      }),
    ).start();
  }, []);

  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-width, width],
  });

  return (
    <View
      style={{
        borderWidth: 1,
        borderColor: '#D7C6FD',
        borderRadius: 10,
        padding: 16,
        height: 50,
        marginVertical: 15,
        marginHorizontal: 16,
        width: '100%',
        alignSelf: 'center',
        overflow: 'hidden',
      }}
    >
      <Animated.View
        style={[
          styles.shimmerOverlay,
          {
            transform: [{ translateX }],
          },
        ]}
      >
        <LinearGradient
          colors={[
            'rgba(255, 255, 255, 0)',
            'rgba(255, 255, 255, 0.3)',
            'rgba(255, 255, 255, 0)',
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{ flex: 1 }}
        />
      </Animated.View>
    </View>
  );
};

export default UserScoreInContestRowShimmer;
