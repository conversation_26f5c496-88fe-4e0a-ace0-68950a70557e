import { CONTESTS_TYPE_TAB_KEYS } from 'modules/contest/constants/contests';

export const CONTEST_STATUS = {
  UPCOMING: 'UPCOMING',
  REGISTRATION_OPEN: 'REGISTRATION_OPEN',
  ONGOING: 'ONGOING',
  ENDED: 'ENDED',
  LIVE: 'LIVE',
};

export const FIELD_TYPES = {
  TEXT: 'TEXT',
  NUMBER: 'NUMBER',
  EMAIL: 'EMAIL',
  MOBILE: 'MOBILE',
  SINGLE_SELECT: 'SINGLE_SELECT',
  MULTI_SELECT: 'MULTI_SELECT',
  CHECKBOX: 'CHECKBOX',
  RADIO: 'RADIO',
  IMAGE_INPUT: 'IMAGE_INPUT',
  FILE_INPUT: 'FILE_INPUT',
};

export const getContestTabBarData = () => [
  {
    key: CONTESTS_TYPE_TAB_KEYS.LEAGUES,
    title: 'LEAGUES',
    image: require('@/assets/images/leagues/tab_icon.png'),
    backgroundColor: '#042639',
    activeColor: '#FE5858',
  },
  {
    key: CONTESTS_TYPE_TAB_KEYS.SHOWDOWNS,
    title: 'SUMDAY \nSHOWDOWN',
    image: require('@/assets/images/icons/sumday_showdown.png'),
    backgroundColor: '#243619',
    activeColor: '#FBD43F',
  },
  {
    key: CONTESTS_TYPE_TAB_KEYS.CONTESTS,
    title: '80 IN 8 \nCONTEST',
    image: require('@/assets/images/icons/80in8.png'),
    backgroundColor: '#040707',
    activeColor: '#A177F4',
  },
];
