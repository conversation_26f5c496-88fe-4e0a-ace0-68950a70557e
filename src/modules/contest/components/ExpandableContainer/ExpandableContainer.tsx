import React, { useState } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import HtmlRenderer from 'atoms/HtmlRenderer';
import dark from 'core/constants/themes/dark';
import styles from './ExpandableContainer.style';

interface ExpandableContainerProps {
  title: string;
  content: string;
}

const ExpandableContainer = ({ title, content }: ExpandableContainerProps) => {
  const [expanded, setExpanded] = useState(false);
  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}
    >
      <TouchableOpacity
        onPress={() => setExpanded(!expanded)}
        style={styles.header}
      >
        <Text style={styles.headerText}>{title}</Text>
        <Icon
          name={expanded ? 'chevron-up-outline' : 'chevron-down-outline'}
          size={20}
          color={dark.colors.textLight}
        />
      </TouchableOpacity>
      {expanded && (
        <View style={styles.content}>
          <HtmlRenderer html={content} />
        </View>
      )}
    </ScrollView>
  );
};

export default React.memo(ExpandableContainer);
