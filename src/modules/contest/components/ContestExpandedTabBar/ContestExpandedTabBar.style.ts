import { StyleSheet } from 'react-native';
import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  mainContainer: {
    padding: 20,
    width: '100%',
  },
  tabSectionContainer: {
    flex: 1,
  },
  scene: {
    flex: 1,
    paddingRight: 16,
    maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
    backgroundColor: dark.colors.card,
    justifyContent: 'center',
    margin: 10,
  },
  text: {
    color: '#FFFFFF',
    fontSize: 14,
    lineHeight: 24,
    fontFamily: 'Montserrat-500',
  },
  tabBarContainer: {
    marginTop: 25,
    width: '100%',
    position: 'relative',
    alignSelf: 'flex-start',
  },
  tabBar: {
    width: '100%',
    backgroundColor: 'transparent',
  },
  indicator: {
    height: 2,
    justifyContent: 'center',
    backgroundColor: dark.colors.secondary,
  },
  tabStyle: {
    width: 130,
  },
  label: {
    fontFamily: 'Montserrat-500',
    fontSize: 14,
  },
  fullWidthLine: {
    position: 'absolute',
    bottom: 0,
    height: 2,
    width: '100%',
    maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
    backgroundColor: dark.colors.tertiary,
  },
});

export default styles;
