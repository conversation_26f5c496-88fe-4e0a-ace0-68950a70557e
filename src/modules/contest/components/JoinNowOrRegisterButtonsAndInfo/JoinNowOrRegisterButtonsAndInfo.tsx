import React, { useCallback, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import groupIcon from 'assets/images/group.png';
import timerIcon from 'assets/images/timer.png';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import _isEmpty from 'lodash/isEmpty';
import useCountDownTimer from 'core/hooks/useCountDownTimer';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import dark from 'core/constants/themes/dark';
import contestReader from 'core/readers/contestReader';
import styles from './JoinNowOrRegisterButtonsAndInfo.style';
import useContestDetailsController from '../../hooks/useContestDetailsController';
import FormModal from '../FormModal';
import JoinNowOrRegisterButtonsShimmer from './JoinNowOrRegisterButtonsShimmer';

interface JoinNowOrRegisterButtonsInfosProps {
  contestDetails: any;
  refetch: any;
  loading: boolean;
  error: any;
}

interface ContestTimerProps {
  startTime: string;
  endTime: string;
  contestIsLiveAndNotEnded: boolean;
}

const ContestTimer = ({
  startTime,
  endTime,
  contestIsLiveAndNotEnded,
}: ContestTimerProps) => {
  const timeLeftToStart = useCountDownTimer({
    targetTime: startTime,
    targetTimeStamp: null,
  });
  const timeLeftToEnd = useCountDownTimer({
    targetTime: endTime,
    targetTimeStamp: null,
  });

  const contestStartOrEndTimeLeftLabel = contestIsLiveAndNotEnded
    ? 'Contest Ends in '
    : 'Contest Starts in ';

  const timeToShow = contestIsLiveAndNotEnded ? timeLeftToEnd : timeLeftToStart;

  return (
    <View style={styles.infoItem}>
      <View style={styles.iconContainer}>
        <Image source={timerIcon} style={styles.iconStyle} />
      </View>
      <View style={styles.info}>
        <Text style={styles.infoText}>{contestStartOrEndTimeLeftLabel}</Text>
        <Text style={styles.infoNumber}>{timeToShow}</Text>
      </View>
    </View>
  );
};

const JoinNowOrRegisterButtonsAndInfos = ({
  contestDetails,
  refetch,
  error,
  loading,
}: JoinNowOrRegisterButtonsInfosProps) => {
  const [isModalVisible, setModalVisible] = useState(false);

  const onContestRegistrationSuccess = useCallback(() => {
    setModalVisible(false);
    refetch?.();
  }, [refetch]);

  const {
    isLive,
    isAboutToStart,
    formFields,
    isSubmitting,
    startTime,
    endTime,
    hasEnded,
    hasCompleted,
    hasUserRegistered,
    handleJoinOrSubmitButtonPress,
    onPressRegisterGuestUser,
    handleFormSubmit,
    onPressUnRegister,
  } = useContestDetailsController({
    contest: contestDetails,
    refetch,
    onContestRegistrationSuccess,
    onRegisterRequest: () => setModalVisible(true),
    onContestRegistrationFailure: () => {},
  });

  const { user } = useSession();
  const canParticipateInTournament =
    userReader.canParticipateInTournaments(user);

  const registrationCount = contestReader.registrationCount(contestDetails);

  const shouldShowRegisterButton = !hasEnded && !hasUserRegistered;
  const contestIsAboutToStartOrLiveAndUserIsRegistered =
    (hasUserRegistered && isLive && !hasEnded) || (!isLive && isAboutToStart);
  const canUserWithdrawRegistration = hasUserRegistered && !hasEnded && !isLive;
  const contestEndedAndUserIsRegistered = hasUserRegistered && hasEnded;
  const showLockedRegisterButtonForGuest =
    !canParticipateInTournament && !hasEnded;
  const contestIsLiveAndNotEnded = !hasEnded && isLive;

  const renderJoinNowOrRegisterButton = () => {
    if (hasCompleted || hasEnded) {
      return null;
    }

    if (showLockedRegisterButtonForGuest) {
      return (
        <TouchableOpacity
          style={styles.lockedRegisterButton}
          onPress={onPressRegisterGuestUser}
        >
          <FontAwesome name="lock" size={20} color={dark.colors.text} />
          <Text style={styles.lockedRegisterText}>Register now</Text>
        </TouchableOpacity>
      );
    }

    if (shouldShowRegisterButton) {
      return (
        <TouchableOpacity
          onPress={handleJoinOrSubmitButtonPress}
          style={styles.registerButton}
        >
          <Text style={styles.registerText}>
            {isSubmitting ? 'Registering...' : 'Register Now'}
          </Text>
        </TouchableOpacity>
      );
    }

    if (contestIsAboutToStartOrLiveAndUserIsRegistered) {
      return (
        <TouchableOpacity
          testID="join-now-button"
          onPress={handleJoinOrSubmitButtonPress}
          style={styles.registerButton}
        >
          <Text style={styles.registerText}>
            {isLive ? 'Join Now' : 'Join Waiting Room'}
          </Text>
        </TouchableOpacity>
      );
    }

    if (contestEndedAndUserIsRegistered) {
      return (
        <TouchableOpacity
          style={styles.registerButton}
          onPress={handleJoinOrSubmitButtonPress}
        >
          <Text style={styles.registerText}>My Result</Text>
        </TouchableOpacity>
      );
    }

    if (canUserWithdrawRegistration) {
      return (
        <TouchableOpacity
          style={styles.unRegisterButton}
          onPress={onPressUnRegister}
        >
          <Text style={styles.unregisterText}>Unregister</Text>
        </TouchableOpacity>
      );
    }

    return null;
  };

  const renderContestTimer = () => (
    <ContestTimer
      startTime={startTime}
      endTime={endTime}
      contestIsLiveAndNotEnded={contestIsLiveAndNotEnded}
    />
  );

  if (hasEnded) {
    return null;
  }

  if (loading) {
    return <JoinNowOrRegisterButtonsShimmer />;
  }

  if (error) {
    return null;
  }

  return (
    <View style={styles.infoContainer}>
      <View style={styles.infoItem}>
        <View style={styles.iconContainer}>
          <Image source={groupIcon} style={styles.iconStyle} />
        </View>
        <View style={styles.info}>
          <Text style={styles.infoText}>Registered</Text>
          <Text style={styles.infoNumber}>{`${registrationCount}`}</Text>
        </View>
      </View>
      {renderContestTimer()}
      <View style={{ marginTop: 20, width: '100%' }}>
        {renderJoinNowOrRegisterButton()}
      </View>
      {!_isEmpty(formFields) && (
        <FormModal
          modalVisible={isModalVisible}
          setModalVisible={setModalVisible}
          fields={formFields}
          onSubmit={handleFormSubmit}
          isLoading={isSubmitting}
        />
      )}
    </View>
  );
};

export default React.memo(JoinNowOrRegisterButtonsAndInfos);
