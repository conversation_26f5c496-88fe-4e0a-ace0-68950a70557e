import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, FlatList, Text, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import dark from 'core/constants/themes/dark';

import ErrorView from 'atoms/ErrorView';
import ContestCard from 'modules/contest/components/ContestCard';
import _isNil from 'lodash/isNil';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';

import ContestCardShimmer from '../../shimmers/ContestCardShimmer';
import useGetContestByStatus from '../../hooks/queries/userGetContestsByStatusQuery';
import { CONTEST_STATUS } from '../../constants';

import useContestListTabStyles from './ContestListsTab.style';

const ContestListsTab = () => {
  const { isMobile: isCompactMode, isTablet } = useMediaQuery();
  const styles = useContestListTabStyles();
  const [contests, setContests] = useState([]);

  const { contestDetail, error, loading, refetch } = useGetContestByStatus({
    statuses: [
      CONTEST_STATUS.UPCOMING,
      CONTEST_STATUS.REGISTRATION_OPEN,
      CONTEST_STATUS.ONGOING,
      CONTEST_STATUS.ENDED,
    ],
  });

  const numOfColumns = isCompactMode ? 2 : isTablet ? 3 : 4;

  useEffect(() => {
    if (contestDetail?.contests) {
      setContests(contestDetail?.contests);
    }
  }, [contestDetail]);

  const renderItem = useCallback(
    ({ item }: { item: any }) => (
      <ContestCard contestDetails={item} key={item._id} />
    ),
    [],
  );

  const renderContent = () => {
    if (loading || _isNil(contestDetail)) {
      return <ContestCardShimmer />;
    }
    if (error) {
      return (
        <ErrorView
          errorMessage="Something went wrong while fetching Contest"
          onRetry={refetch}
        />
      );
    }
    if (contests.length === 0) {
      return (
        <View
          style={{
            height: '80%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <FontAwesome5 name="trophy" size={48} color={dark.colors.tertiary} />
          <Text style={styles.noContestText}>No contests to show</Text>
        </View>
      );
    }
    return (
      <FlatList
        key={`contest-list-${numOfColumns}`}
        data={contestDetail?.contests}
        keyExtractor={(item, index) => `${item._id.toString()}-${index}`}
        renderItem={renderItem}
        numColumns={numOfColumns}
        columnWrapperStyle={styles.columnWrapper}
        showsVerticalScrollIndicator={false}
        ListFooterComponent={
          loading ? <ActivityIndicator size="small" color="#FFFFFF" /> : null
        }
        style={{ marginHorizontal: isCompactMode ? '5%' : 0 }}
      />
    );
  };

  return <View style={styles.contestListTabContainer}>{renderContent()}</View>;
};

export default React.memo(ContestListsTab);
