import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from 'core/constants/themes/dark';

const createStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'transparent',
      width: '100%',
      marginTop: 15,
      borderColor: 'transparent',
      borderWidth: 0,
    },
    tabContainer: {
      overflowX: 'hidden',
      flexDirection: 'row',
      justifyContent: 'flex-start',
      marginBottom: 20,
      marginHorizontal: 16,
    },
    tab: {
      paddingHorizontal: 30,
      paddingVertical: 10,
      borderRadius: 20,
      backgroundColor: dark.colors.tertiary,
      marginHorizontal: 6,
    },
    activeTab: {
      paddingHorizontal: 30,
      paddingVertical: 10,
      borderRadius: 20,
      borderColor: dark.colors.secondary,
      borderWidth: 1,
      backgroundColor: dark.colors.tertiary,
      marginHorizontal: 6,
    },
    tabText: {
      color: '#999',
      fontSize: 14,
      fontFamily: 'Montserrat-600',
    },
    activeTabText: {
      fontSize: 14,
      fontFamily: 'Montserrat-600',
      color: dark.colors.secondary,
    },
    contestList: {
      // marginVertical: 20,
      // marginHorizontal:16,
    },
    cardContainer: {
      width: 180,
      marginHorizontal: 10,
      borderRadius: 15,
      overflow: 'hidden',
    },
    cardGradient: {
      flex: 1,
      padding: 15,
      justifyContent: 'space-between',
    },
    cardContent: {
      alignItems: 'center',
    },
    status: {
      fontSize: 12,
      color: dark.colors.textLight,
      backgroundColor: '#666',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 10,
      overflow: 'hidden',
      alignSelf: 'flex-start',
    },
    trophyIconPlaceholder: {
      width: 50,
      height: 50,
      backgroundColor: '#fff',
      borderRadius: 25,
      marginVertical: 10,
    },
    cardTitle: {
      fontSize: 18,
      color: '#fff',
      fontFamily: 'Montserrat-700',
      textAlign: 'center',
    },
    cardDate: {
      fontSize: 14,
      color: '#ccc',
      textAlign: 'center',
    },
    noContestText: {
      color: dark.colors.textDark,
      fontSize: 15,
      width: 200,
      fontFamily: 'Montserrat-500',
      textAlign: 'center',
      marginLeft: 20,
      marginTop: 20,
      marginBottom: 5,
    },
    columnWrapper: {
      justifyContent: isCompactMode ? 'space-between' : 'flex-start',
      gap: !isCompactMode ? 20 : 0,
      marginBottom: 15,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    contestListTabContainer: {
      flex: 1,
      marginTop: 16,
    },
  });

const useContestListTabStyles = () => {
  const { isMobile } = useMediaQuery();

  return useMemo(() => createStyles(isMobile), [isMobile]);
};

export default useContestListTabStyles;
