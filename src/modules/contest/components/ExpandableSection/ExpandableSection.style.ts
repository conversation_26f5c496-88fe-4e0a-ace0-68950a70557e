import dark from 'core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  expandableHeader: {
    marginHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'transparent',
    alignItems: 'center',
    height: 60,
    borderBottomColor: 'grey',
    borderBottomWidth: 0.3,
    paddingVertical: 10,
  },
  expandableText: {
    color: dark.colors.textDark,
    fontSize: 14,
    lineHeight: 24,
    fontFamily: 'Montserrat-500',
  },
  expandedContent: {
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
});

export default styles;
