import { StyleSheet } from "react-native";

import Dark from 'core/constants/themes/dark'
import dark from "../../../../core/constants/themes/dark";

const styles = StyleSheet.create({
  gradient: {
    height: 'auto'
  },
  container: {
    overflow: 'hidden',
    flex: 1,
    width: '100%',
    height: '100%',
    // backgroundColor: dark.colors.primary
  },
  header: {
    display: 'flex',
    flexDirection: 'row'
  },
  headerTitle: {
    color: '#fff',
    fontSize: 25,
    fontFamily: 'Montserrat-700',
  },
  contestDetails: {
    marginLeft: 16,
    marginRight: 16,
    width: '100%',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'row',
    gap: 12,
  },
  contestTimeAndDesc: {
    borderRadius: 8,
    flex: 1,
    marginBottom: 10,
    gap: 5,
  },
  contestImage: {
    width: 50,
    height: 50,
    marginBottom: 10,
  },
  contestTitle: {
    color: '#fff',
    fontFamily: 'Montserrat-600',
    fontSize: 16,
    lineHeight: 20,
    flexShrink: 1
  },
  hostedBy: {
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-600',
    fontSize: 10,
    flexWrap: 'wrap',
    lineHeight: 12,
    display: 'flex',
    marginRight: 16,
  },
  gradientBox: {
    width: 60,
    height: 60,
    marginBottom: 15,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  hostedByLogo: {
    width: 60,
    height: 60,
    resizeMode: 'contain',
    borderRadius: 10,
  },
  detailsRow: {
    width: '95%',
    flexDirection: 'row',
    height: 'auto',
    marginRight: 16,
    gap: 4
  },
  detailsText: {
    flex: 2,
    color: '#FFFFFF',
    fontFamily: 'Montserrat-600',
    fontSize: 12,
    lineHeight: 15,
    marginBottom: 5
  },
  expandableHeader: {
    marginHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'transparent',
    alignItems: 'center',
    height: 60,
    borderBottomColor: 'grey',
    borderBottomWidth: 0.3,
    paddingVertical: 10,
  },
  expandableText: {
    color: dark.colors.textDark,
    fontSize: 14,
    lineHeight: 24,
    fontFamily: 'Montserrat-500'
  },
  expandedContent: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    // backgroundColor: '#2A2A2A',
  },
  contestDetailContainer: {
    paddingVertical: 10,
    paddingBottom: 80,
  },
  aboutText: {
    color: dark.colors.textDark,
    fontSize: 14,
    fontFamily: 'Montserrat-500',
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#00ff66',
    padding: 15,
    borderRadius: 50,
    alignItems: 'center',
    // elevation: 8,
  },
  fabText: {
    fontSize: 16,
    color: dark.colors.card,
    fontFamily: 'Montserrat-600'
  },
  unRegisterButton: {
    backgroundColor: dark.colors.primary,
    paddingVertical: 15,
    borderRadius: 50,
    width:'90%',
    alignItems: 'center',
  },
  unregisterText: {
    fontFamily: 'Montserrat-600',
    fontSize: 13,
    lineHeight: 20,
    color: '#FF7777'
  },
  lockedRegisterButton: {
    backgroundColor: Dark.colors.tertiary,
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    width: '90%',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  lockedRegisterText: {
    color: Dark.colors.text,
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
  shimmerHeaderButton: {
    width: 25,
    height: 25,
    borderRadius: 12.5,
  },
  shimmerHeaderText: {
    width: 100,
    height: 20,
    borderRadius: 4,
  },
  shimmerImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginBottom: 10,
  },
  shimmerTitle: {
    width: '60%',
    height: 20,
    borderRadius: 4,
    marginBottom: 5,
  },
  shimmerSubText: {
    width: '40%',
    height: 15,
    borderRadius: 4,
    marginBottom: 10,
  },
  shimmerDetailsRow: {
    width: '100%',
    height: 20,
    borderRadius: 4,
  },
  shimmerExpandable: {
    width: '100%',
    height: 40,
    borderRadius: 4,
    marginTop: 10,
  },
  shimmerFab: {
    width: 150,
    height: 50,
    borderRadius: 25,
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  tabSectionContainer: {
    flex: 1,
  },
  scene: {
    height: 'auto',
    justifyContent: 'center',
  },
  text: {
    color: '#FFFFFF',
    fontSize: 14,
    lineHeight: 24,
    fontFamily: 'Montserrat-500',
  },
  tabBarContainer: {
    width: '100%',
    alignSelf: 'flex-start',
    justifyContent: 'space-between'
  },
  tabBar: {
    width: '100%',
    backgroundColor: dark.colors.background,
    borderBottomColor: dark.colors.tertiary,
    borderBottomWidth: 2,
  },
  indicator: {
    height: 4,
    justifyContent: 'center',
    borderTopRightRadius: 5,
    borderTopLeftRadius: 5,
    backgroundColor: dark.colors.secondary,
  },
  tabStyle: {
    flexGrow: 1,
    alignItems: 'center',
    overflow: 'hidden',
    justifyContent: 'space-between',
  },
  label: {
    fontFamily: 'Montserrat-500',
    fontSize: 14,
    lineHeight: 20,
  },
  registerNowDetail: {
    display: 'flex',
    flexDirection: 'row',
    gap: 5,
    justifyContent: 'center',
  },
  liveDetail: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10,
  },
  registrationEnds: {
    color: dark.colors.offWhite,
    fontSize: 12,
    fontFamily: 'Montserrat-400',
  },
  daysLeftDetails: {
    color: '#D3BFFF',
    fontSize: 12,
    fontFamily: 'Montserrat-700',
  },
  liveText: {
    color: '#CC1B1B',
    fontSize: 14,
    fontFamily: 'Montserrat-600',
  },
  liveContainer: {
    backgroundColor: dark.colors.error,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 3,
    marginRight: 16,
  },
  registeredContainer: {
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    paddingHorizontal: 20,
    borderRadius: 20,
    marginVertical: 10,
  },
  checkedIcon: {
    marginRight: 10,
  },
  registeredMessageText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontFamily: 'Montserrat-600',
  },
});

export default styles