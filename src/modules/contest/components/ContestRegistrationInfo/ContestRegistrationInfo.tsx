import _isEmpty from 'lodash/isEmpty';
import { Text, View } from 'react-native';
import React from 'react';
import _isNil from 'lodash/isNil';

import StackedUserImages from 'shared/StackedUserImage';
import numeral from 'numeral';
import styles from './ContestRegistrationInfo.style';

const NUMERAL_FORMAT = '0[.]00a';

interface ContestRegistrationInfoProps {
  contest: any;
  size?: number;
  textStyle?: any;
}

const ContestRegistrationInfo = ({
  contest,
  size = 28,
  textStyle = EMPTY_OBJECT,
}: ContestRegistrationInfoProps) => {
  const { recentParticipants, registrationCount } = contest;

  const suffix = registrationCount > 1000 ? '+' : '';

  return (
    <View style={styles.container}>
      <StackedUserImages users={recentParticipants} imageSize={size} />
      <Text style={[styles.registrationCount, textStyle]}>
        {numeral(registrationCount).format(NUMERAL_FORMAT)}
        {suffix}
      </Text>
    </View>
  );
};

const ContestRegistrationInfoContainer = React.forwardRef(
  (props: ContestRegistrationInfoProps, ref) => {
    const { contest } = props;

    if (_isEmpty(contest) || _isNil(contest)) {
      return null;
    }

    return <ContestRegistrationInfo ref={ref} {...props} />;
  },
);

export default React.memo(ContestRegistrationInfoContainer);
