import React from 'react';
import { Text, View } from 'react-native';
import LinearGradient from '../../../../components/atoms/LinearGradient';
import dark from 'core/constants/themes/dark';
import styles from './LeaderboardItem.style';

interface LeaderboardItemProps {
  name: string;
  score: number;
  position: number;
}

const LeaderboardItem = ({ name, score, position }: LeaderboardItemProps) => {
  if (position == 1) {
    return (
      <LinearGradient
        colors={dark.colors.congratulaotory}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradientBox}
      >
        <View style={styles.innerContainer}>
          <View style={styles.nameAndPosition}>
            <Text style={styles.position}>#{position}</Text>
            <Text style={styles.name} numberOfLines={1}>
              {name}
            </Text>
          </View>
          <Text style={styles.score} numberOfLines={1}>
            {score}
          </Text>
        </View>
      </LinearGradient>
    );
  }
  return (
    <View
      style={[
        styles.itemContainer,
        position === 1
          ? styles.firstItem
          : position === 2
            ? styles.secondItem
            : position === 3
              ? styles.thirdItem
              : null,
      ]}
    >
      <View style={styles.nameAndPosition}>
        <Text style={styles.position} numberOfLines={1}>
          #{position}
        </Text>
        <Text style={styles.name} numberOfLines={1}>
          {name}
        </Text>
      </View>
      <Text style={styles.score} numberOfLines={1}>
        {score}
      </Text>
    </View>
  );
};

export default React.memo(LeaderboardItem);
