import React, { useCallback, useState } from 'react';
import {
  Dimensions,
  Image,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import useMediaQuery from 'core/hooks/useMediaQuery';
import textureImage from '@/assets/images/backgrounds/contests_tab_bg.png';
import { router, useLocalSearchParams } from 'expo-router';
import { getContestTabBarData } from 'modules/contest/constants';
import ContestListsTab from 'modules/contest/components/ContestListsTab';
import ContestTabBarShimmer from '../../shimmers/ContestDetailTabBarShimmer';
import {
  checkIsValidContestTypeTab,
  CONTESTS_TYPE_TAB_KEYS,
} from '../../constants/contests';

import ShowdownListTabs from '../ShowdownCard/ShowdownListTabs';
import LeaguesHomePage from '../../../league/pages/LeaguesHomePage';
import styles from './contestTabBar.style';

const initialLayout = { width: Dimensions.get('window').width };

const Contests = () => <ContestListsTab />;

const Showdowns = () => <ShowdownListTabs />;

const ContestTabBar = ({ contestDetails, loading }) => {
  const { tab } = useLocalSearchParams();
  const [activeTab, setActiveTab] = useState(
    checkIsValidContestTypeTab(tab) ? tab : CONTESTS_TYPE_TAB_KEYS.LEAGUES,
  );

  const { isMobile: isCompactMode } = useMediaQuery();

  const navigationItems = getContestTabBarData();

  const renderContent = useCallback(() => {
    switch (activeTab) {
      case CONTESTS_TYPE_TAB_KEYS.SHOWDOWNS:
        return <Showdowns />;
      case CONTESTS_TYPE_TAB_KEYS.CONTESTS:
        return <Contests />;
      case CONTESTS_TYPE_TAB_KEYS.LEAGUES:
        return <LeaguesHomePage />;
      default:
        return null;
    }
  }, [activeTab]);

  if (loading && _isEmpty(contestDetails)) {
    return <ContestTabBarShimmer />;
  }

  return (
    <View style={{ flex: 1 }}>
      <View style={{ height: 60 }}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.scrollContainer}
          bounces
          alwaysBounceHorizontal
          overScrollMode="always"
        >
          {navigationItems.map((item, index) => (
            <TouchableOpacity
              key={item.key}
              onPress={() => {
                setActiveTab(item.key);
                router.setParams({
                  tab: item.key,
                });
              }}
              style={[
                styles.itemContainer,
                {
                  borderColor:
                    activeTab === item.key ? item.activeColor : 'transparent',
                },
                { marginLeft: index === 0 ? 10 : 0 },
              ]}
            >
              <View style={styles.itemImageContainer}>
                <View
                  style={[
                    styles.backgroundColorView,
                    { backgroundColor: item.backgroundColor },
                  ]}
                />
                <Image source={textureImage} style={styles.itemImage} />
                <View style={styles.contentContainer}>
                  <Text style={[styles.title, { color: item.activeColor }]}>
                    {item.title}
                  </Text>
                  <Image source={item.image} style={styles.contentIcon} />
                </View>
              </View>
              {/* <Text
              style={
                activeTab === item.key
                  ? styles.activeItemText
                  : styles.itemText
              }
            >
              {item.title}
            </Text> */}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      {renderContent()}
    </View>
  );
};

export default ContestTabBar;
