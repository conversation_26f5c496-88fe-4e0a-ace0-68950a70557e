import { StyleSheet } from 'react-native';
import dark from '../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  bottomSheetContent: {
    backgroundColor: dark.colors.card,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  disabledButton: {
    opacity: 0.5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 35,
  },
  headerText: {
    fontSize: 18,
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-500',
  },
  closeIcon: {
    color: dark.colors.textLight,
    fontSize: 18,
  },
  inputLabelBox: {
    flex: 1,
    display: 'flex',
    flexDirection: 'row',
    gap: 10,
    marginRight: 10,
  },
  inputContainer: {
    marginBottom: 25,
  },
  inputLabel: {
    fontSize: 14,
    color: dark.colors.textLight,
    marginBottom: 5,
    fontFamily: 'Montserrat-500',
  },
  inputLabelMandatory: {
    fontSize: 16,
    color: dark.colors.error,
    marginBottom: 5,
    fontFamily: 'Montserrat-700',
  },
  inputField: {
    borderWidth: 2,
    borderColor: dark.colors.tertiary,
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    color: dark.colors.textLight,
    backgroundColor: '#333',
  },
  focusedInputField: {
    outlineColor: dark.colors.secondary,
    borderWidth: 1,
    outlineWidth: 1,
    borderColor: 'white',
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    color: dark.colors.textLight,
    backgroundColor: 'transparent',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 10,
    marginTop: 20,
    marginLeft: 50,
  },
  cancelButton: {
    backgroundColor: 'transparent',
    padding: 12,
    borderRadius: 10,
    flex: 1,
    alignItems: 'center',
  },
  submitButton: {
    backgroundColor: dark.colors.secondary,
    padding: 12,
    borderRadius: 10,
    flex: 1,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: dark.colors.secondary,
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
  submitButtonText: {
    color: dark.colors.card,
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
});

export default styles;
