import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      backgroundColor: '#040707',
      borderRadius: 12,
      marginHorizontal: isCompactMode ? 0 : 0,
      alignItems: 'center',
      width: isCompactMode ? '48%' : 180,
      height: 'auto',
      overflow: 'hidden',
      position: 'relative',
    },
    imageContainer: {
      borderRadius: 12,
      paddingTop: 20,
      paddingBottom: 14,
      alignItems: 'center',
      width: isCompactMode ? '100%' : 180,
      height: 'auto',
      overflow: 'hidden',
    },
    iconContainer: {
      marginTop: 10,
      width: isCompactMode ? 65 : 150,
      height: isCompactMode ? 60 : 90,
      borderRadius: 10,
      justifyContent: 'center',
      alignItems: 'center',
    },
    logoStyle: {
      width: isCompactMode ? 70 : 80,
      height: isCompactMode ? 80 : 90,
    },
    date: {
      marginTop: 15,
      color: '#FFFFFF',
      fontSize: isCompactMode ? 14 : 12,
      fontFamily: 'Montserrat-700',
      marginHorizontal: 10,
    },
    time: {
      marginTop: 5,
      color: '#FFFFFF',
      fontSize: isCompactMode ? 10 : 12,
      fontFamily: 'Montserrat-600',
      marginHorizontal: 10,
    },

    liveBadge: {
      position: 'absolute',
      top: 12,
      right: 12,
      height: 13,
      width: 28,
      backgroundColor: '#F84E4E',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 12,
    },
    liveBadgeText: {
      color: 'white',
      fontFamily: 'Montserrat-500',
      paddingHorizontal: 8,
      fontSize: isCompactMode ? 2 : 5,
      letterSpacing: 1,
    },

    statusIcon: {
      marginRight: 4,
      width: 12,
      height: 12,
    },

    statusRegistered: {
      color: '#BABABA',
      fontSize: isCompactMode ? 12 : 10,
      fontFamily: 'Montserrat-700',
      fontWeight: 'bold',
    },

    liveContainer: {
      marginTop: 15,
      width: '100%',
      height: 20,
      backgroundColor: '#FBD43F',
      opacity: 0.9,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 12,
    },
  });

const useContestCardStyles = () => {
  const { isMobile } = useMediaQuery();
  const styles = useMemo(() => createStyles(isMobile), [isMobile]);
  return styles;
};

export default useContestCardStyles;
