import React, { useCallback } from 'react';
import {
  Image,
  ImageBackground,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useRouter } from 'expo-router';
import TextureImage from '@/assets/images/backgrounds/contests_tab_bg.png';
import OptiverIcon from '@/assets/images/icons/80in8.png';
import contestReader from 'core/readers/contestReader';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Analytics from 'core/analytics';
import ContestStatusMessage from 'modules/contest/components/ContestStatusMessage/ContestStatusMessage';
import useContestCardStyles from './ContestCard.style';
import { CONTEST_STATUS } from '../../constants';
import { getContestPropertiesToTrack } from '../../utils/contestEvents';

interface ContestCardProps {
  contestDetails: any;
}

const ContestCard = ({ contestDetails }: ContestCardProps) => {
  const contestId = contestReader.id(contestDetails);

  const styles = useContestCardStyles();
  const router = useRouter();

  const onPress = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.CONTEST.CLICKED_ON_CONTEST_CARD, {
      ...getContestPropertiesToTrack({ contest: contestDetails }),
      [PAGE_NAME_KEY]: PAGE_NAMES.CONTESTS,
    });

    router.push(`/contest/${contestId}`);
  }, [router, contestId, contestDetails]);

  const renderIconContainer = () => (
    <View style={styles.iconContainer}>
      <Image source={OptiverIcon} style={styles.logoStyle} />
    </View>
  );

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <ImageBackground
        source={TextureImage}
        style={[styles.imageContainer]}
        resizeMode="cover"
      >
        {contestDetails?.status === CONTEST_STATUS.ONGOING && (
          <View style={styles.liveBadge}>
            <Text
              style={{
                fontSize: 7,
                fontFamily: 'Montserrat-700',
                color: 'white',
                letterSpacing: 1,
              }}
            >
              LIVE
            </Text>
          </View>
        )}
        {renderIconContainer()}
        <Text style={styles.date} numberOfLines={1}>
          {new Date(contestDetails.startTime).toLocaleDateString('en-GB', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
          })}
        </Text>
        <Text style={styles.time}>
          {new Date(contestDetails.startTime).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: 'numeric',
          })}
        </Text>

        <ContestStatusMessage contestDetails={contestDetails} />
      </ImageBackground>
    </TouchableOpacity>
  );
};

export default React.memo(ContestCard);
