import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Dimensions, Text, View } from 'react-native';
import { TabBar, TabView } from 'react-native-tab-view';
import Analytics from 'core/analytics';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import dark from 'core/constants/themes/dark';
import CompactContestLeaderboard from 'modules/contest/components/CompactContestLeaderboard';

import CompactContestDetailsTabContent from 'modules/contest/components/CompactContestDetailsTabContent';
import styles from '../CompactContestDetail/CompactContestDetail.style';
import ContestScoreAnalysis from '../ScoreAnalysis';

import { TAB_KEY_VS_EVENT, TAB_KEYS } from '../../constants/contestDetails';
import { getContestPropertiesToTrack } from '../../utils/contestEvents';

const initialLayout = { width: Dimensions.get('window').width };

interface CompactContestDetailsTabProps {
  contestDetails: any;
  isLive: boolean;
  hasUserRegistered: boolean;
  hasEnded: boolean;
}

const CompactContestDetailsTab = ({
  contestDetails,
  isLive,
  hasUserRegistered,
  hasEnded,
}: CompactContestDetailsTabProps) => {
  const [index, setIndex] = useState(0);
  const routes = useMemo(() => {
    const availableRoutes = [];
    if (isLive || hasEnded) {
      availableRoutes.push({
        key: TAB_KEYS.RESULT,
        title: isLive ? 'Live Leaderboard' : 'Results',
      });
    }
    if (hasUserRegistered && hasEnded) {
      availableRoutes.push({ key: TAB_KEYS.ANALYSIS, title: 'Analysis' });
    }
    availableRoutes.push({ key: TAB_KEYS.DETAILS, title: 'Details' });
    return availableRoutes;
  }, [isLive, hasUserRegistered, hasEnded]);

  const onIndexChange = useCallback(
    (updatedIndex: number) => {
      setIndex(updatedIndex);
      const route = routes[updatedIndex];
      if (TAB_KEY_VS_EVENT?.[route?.key]) {
        Analytics.track(TAB_KEY_VS_EVENT[route.key], {
          ...getContestPropertiesToTrack({ contest: contestDetails }),
          [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_DETAILS,
        });
      }
    },
    [routes, contestDetails],
  );

  const renderScene = useCallback(
    ({ route }: { route: any }) => {
      switch (route.key) {
        case TAB_KEYS.RESULT:
          return <CompactContestLeaderboard contest={contestDetails} />;
        case TAB_KEYS.ANALYSIS:
          return <ContestScoreAnalysis contestDetails={contestDetails} />;
        case TAB_KEYS.DETAILS:
          return (
            <CompactContestDetailsTabContent contestDetails={contestDetails} />
          );
        default:
          return null;
      }
    },
    [contestDetails],
  );

  const renderTabBar = useCallback(
    (props: any) =>
      routes.length > 1 ? (
        <View style={styles.tabBarContainer}>
          <TabBar
            {...props}
            indicatorStyle={styles.indicator}
            style={styles.tabBar}
            tabStyle={styles.tabStyle}
            labelStyle={styles.label}
            activeColor={dark.colors.secondary}
            inactiveColor={dark.colors.textDark}
            renderLabel={({ route, color }) => (
              <Text style={{ ...styles.label, color }}>{route.title}</Text>
            )}
          />
          <View style={styles.fullWidthLine} />
        </View>
      ) : null,
    [routes],
  );

  useEffect(() => {
    if (index >= routes.length) {
      setIndex(0);
    }
  }, [routes, index]);

  return (
    <TabView
      navigationState={{ index, routes }}
      renderScene={renderScene}
      onIndexChange={onIndexChange}
      initialLayout={initialLayout}
      renderTabBar={renderTabBar}
    />
  );
};

export default React.memo(CompactContestDetailsTab);
