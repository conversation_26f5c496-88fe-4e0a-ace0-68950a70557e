import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    width: '90%',
    marginHorizontal: '10%',
  },
  title: {
    color: dark.colors.textLight,
    fontSize: 20,
    fontFamily: 'Montserrat-700',
    maxWidth: '80%',
  },
  list: {
    flexGrow: 1,
  },
  row: {
    flexDirection: 'row',
    gap: 10,
    paddingVertical: 18,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: dark.colors.primary,
  },
  rank: {
    textAlign: 'left',
    flex: 1,
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-500',
    fontSize: 13,
  },
  name: {
    textAlign: 'left',
    color: dark.colors.textLight,
    maxWidth: 160,
    flex: 3,
    fontFamily: 'Montserrat-500',
    fontSize: 13,
  },
  score: {
    color: dark.colors.textLight,
    flex: 1,
    fontFamily: 'Montserrat-500',
    fontSize: 13,
    textAlign: 'right',
  },
  time: {
    flex: 2,
    textAlign: 'right',
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-500',
    fontSize: 13,
  },
  paginationContainer: {
    backgroundColor: dark.colors.background,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 6,
    borderRadius: 10,
    bottom: 0,
    position: 'absolute',
    alignItems: 'center',
  },
  pageButton: {
    marginHorizontal: 5,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 10,
  },
  selectedPage: {
    backgroundColor: dark.colors.primary,
  },
  pageText: {
    color: dark.colors.textDark,
    fontSize: 16,
  },
  selectedPageText: {
    color: dark.colors.secondary,
    fontSize: 16,
  },
});

export default styles;
