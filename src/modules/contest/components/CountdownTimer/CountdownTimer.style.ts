import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    backgroundColor: 'transparent',
    gap: 8,
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    gap: 15,
    marginTop: 10,
  },
  headerTitle: {
    fontSize: 25,
  },
  hostDetail: {
    display: 'flex',
    flexDirection: 'column',
    gap: 5,
    marginBottom: 20,
  },
  registerNowDetail: {
    display: 'flex',
    flexDirection: 'row',
    gap: 5,
    justifyContent: 'center',
  },
  liveDetail: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10,
  },
  mainContainerStyle: {
    padding: 20,
    backgroundColor: 'transparent',
    gap: 8,
  },
  liveText: {
    color: '#CC1B1B',
    fontSize: 14,
    fontFamily: 'Montserrat-600',
    lineHeight: 14,
  },
  liveContainer: {
    backgroundColor: dark.colors.error,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 5,
    marginBottom: 5,
  },
  title: {
    color: 'white',
    fontSize: 18,
    fontFamily: 'Montserrat-600',
  },
  hostedBy: {
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-400',
    fontSize: 14,
  },
  hostName: {
    fontFamily: 'Montserrat-600',
    color: dark.colors.textDark,
    fontSize: 14,
  },
  startsInBox: {
    backgroundColor: '#D7C5FF',
    paddingHorizontal: 8,
    width: 160,
    borderRadius: 8,
    alignItems: 'center',
    height: 30,
    justifyContent: 'center',
  },
  startsInText: {
    color: dark.colors.contestText,
    textAlign: 'left',
    fontFamily: 'Montserrat-600',
    fontSize: 14,
  },
  infoRow: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 17,
  },
  infoBox: {
    flex: 1,
    gap: 10,
    marginRight: 10,
  },
  buttonBox: {
    flex: 1,
    gap: 1,
    marginRight: 10,
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  infoTitle: {
    color: dark.colors.textDark,
    fontSize: 14,
    fontFamily: 'Montserrat-400',
  },
  infoDetails: {
    color: dark.colors.textLight,
    fontSize: 15,
    fontFamily: 'Montserrat-600',
  },
  daysLeftDetails: {
    color: '#D3BFFF',
    fontSize: 12,
    fontFamily: 'Montserrat-600',
  },
  registerButton: {
    backgroundColor: dark.colors.secondary,
    padding: 10,
    height: 36,
    width: 180,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  lockedRegisterButton: {
    backgroundColor: dark.colors.tertiary,
    padding: 10,
    height: 36,
    width: 180,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  successfullyRegistered: {
    backgroundColor: dark.colors.primary,
    padding: 10,
    height: 40,
    width: 220,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  successfullyRegisteredText: {
    color: '#9ECDF9',
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
  registerText: {
    color: dark.colors.card,
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
  lockedRegisterText: {
    color: dark.colors.text,
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
  registrationEnds: {
    color: dark.colors.textLight,
    fontSize: 12,
    marginBottom: 20,
    textAlign: 'center',
  },
  gradientBox: {
    width: 55,
    height: 55,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  hostedByLogo: {
    width: 55,
    height: 55,
    borderRadius: 10,
    resizeMode: 'contain',
  },
  buttonContainer: {
    gap: 8,
  },
});

export default styles;
