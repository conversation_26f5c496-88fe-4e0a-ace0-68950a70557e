import { StyleSheet } from 'react-native';

import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';

const getContestStatusStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    statusContainer: {
      marginTop: 10,
      width: '100%',
      height: 20,
      backgroundColor: '#A87CFF',
      opacity: 0.9,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
    },
    statusText: {
      color: '#30234A',
      fontSize: isCompactMode ? 8 : 9,
      fontFamily: 'Montserrat-700',
      fontWeight: 'bold',
    },
    statusLiveContainer: {
      marginTop: 10,
      width: isCompactMode ? '70%' : '80%',
      height: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 12,
    },

    statusLiveText: {
      color: 'white',
      fontSize: isCompactMode ? 8 : 8,
      fontFamily: 'Montserrat-800',
      fontWeight: 'bold',
      letterSpacing: 0.5,
    },
  });
const useContestStatusStyles = () => {
  const { isMobile } = useMediaQuery();
  return useMemo(() => getContestStatusStyles(isMobile), [isMobile]);
};

export default useContestStatusStyles;
