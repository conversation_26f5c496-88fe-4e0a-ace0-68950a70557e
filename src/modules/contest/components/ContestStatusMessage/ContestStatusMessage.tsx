import React from 'react';
import { Text, View } from 'react-native';
import _isNil from 'lodash/isNil';
import { CONTEST_STATUS } from 'modules/contest/constants';

import Rive from 'atoms/Rive';
import useCountDownTimer from 'core/hooks/useCountDownTimer';
import useContestStatusStyles from './ContestStatusMessage.style';

const ContestStatusMessage = ({ contestDetails }) => {
  const styles = useContestStatusStyles();
  const isContestCompleted = contestDetails.status === CONTEST_STATUS.ENDED;

  const arrowAnimation =
    'https://cdn.matiks.com/files/668cd7fa7f82ca977f9a6d90_contest_arrow.riv?timestamp=173822158';

  const timeLeftToStart = useCountDownTimer({
    targetTime: contestDetails?.startTime,
  });

  const renderRegistrationOpenStatusMessage = () => (
    <View style={styles.statusContainer}>
      <Text style={styles.statusText}>{`STARTS IN ${timeLeftToStart}`}</Text>
    </View>
  );

  const renderJoinNowStatusMessage = () => (
    <View style={styles.statusLiveContainer}>
      <Text style={styles.statusLiveText}>JOIN NOW</Text>
      <Rive url={arrowAnimation} autoPlay style={{ width: 30, height: 20 }} />
    </View>
  );

  const renderRegistrationEndsStatusMessage = () => (
    <View style={styles.statusContainer}>
      <Text style={styles.statusText}>
        {`REGISTRATION ENDS IN ${timeLeftToStart}`}
      </Text>
    </View>
  );

  const userRegisteredForContest = !_isNil(
    contestDetails?.currentUserParticipation,
  );

  const renderNotCompletedContestStatusMessage = () => {
    // User not registered
    if (userRegisteredForContest) {
      // Registration open
      if (contestDetails?.status === CONTEST_STATUS.REGISTRATION_OPEN) {
        return renderRegistrationOpenStatusMessage();
      }

      // Contest not started
      return renderJoinNowStatusMessage();
    }

    // User not registered
    if (contestDetails?.status === CONTEST_STATUS.ONGOING) {
      return renderJoinNowStatusMessage();
    }

    return renderRegistrationEndsStatusMessage();
  };

  const renderVirtualContestStatusMessage = () => (
    <View style={styles.statusLiveContainer}>
      <Text style={styles.statusLiveText}>VIRTUAL CONTEST</Text>
      <Rive url={arrowAnimation} autoPlay style={{ width: 30, height: 20 }} />
    </View>
  );

  const renderCompletedContestStatusMessage = () => {
    // User not participated
    if (_isNil(contestDetails?.currentUserParticipation)) {
      return renderVirtualContestStatusMessage();
    }

    // User participated
    return (
      <View style={styles.statusLiveContainer}>
        <Text style={styles.statusLiveText}>
          {`YOUR SCORE - ${contestDetails?.currentUserParticipation?.score}`}
        </Text>
      </View>
    );
  };

  if (!isContestCompleted) {
    return renderNotCompletedContestStatusMessage();
  }

  // Completed contest scenarios
  return renderCompletedContestStatusMessage();
};

export default React.memo(ContestStatusMessage);
