import _isEmpty from 'lodash/isEmpty';
import React, { useMemo } from 'react';
import { Image, Text, View } from 'react-native';
import ErrorView from 'atoms/ErrorView';
import LinearGradient from 'atoms/LinearGradient';
import dark from 'core/constants/themes/dark';
import useContestDetailsController from '../../hooks/useContestDetailsController';
import styles from './ContestDetailCard.style';

interface ContestDetailCardProps {
  contestDetails: any;
  loading: boolean;
  error: boolean;
  refetch: () => void;
}

const ContestDetailCard = ({
  contestDetails,
  refetch,
}: {
  contestDetails: any;
  refetch: () => void;
}) => {
  const { isLive, hasEnded } = useContestDetailsController({
    contest: contestDetails,
    refetch,
  });

  const headerContent = useMemo(
    () => (
      <View style={styles.header}>
        <View>
          <LinearGradient
            colors={dark.colors.contestLogoBgGradient}
            style={styles.gradientBox}
          >
            <View style={styles.iconContainer}>
              {contestDetails?.hostedByV2?.logo ? (
                <Image
                  source={{ uri: contestDetails?.hostedByV2?.logo }}
                  style={styles.hostedByLogo}
                />
              ) : (
                <Text style={styles.headerTitle}>🏆</Text>
              )}
            </View>
          </LinearGradient>
        </View>
        <View>
          <View style={styles.liveDetail}>
            <Text style={styles.title}>{contestDetails?.name}</Text>
            {isLive && (
              <View style={styles.liveContainer}>
                <Text style={styles.liveText}>Live</Text>
              </View>
            )}
            {hasEnded && (
              <View style={styles.liveContainer}>
                <Text style={styles.liveText}>Completed</Text>
              </View>
            )}
          </View>
          <View style={styles.hostDetail}>
            <Text style={styles.hostedBy}>
              {`Hosted By ${contestDetails?.hostedByV2?.name ?? 'Matiks'}`}
            </Text>
          </View>
        </View>
      </View>
    ),
    [contestDetails, isLive, hasEnded],
  );

  const infoRowContent = useMemo(
    () => (
      <View style={styles.infoRow}>
        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>Contest open time</Text>
          <Text style={styles.infoDetails}>
            {new Date(contestDetails?.startTime).toLocaleString()}
          </Text>
        </View>
        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>Contest close time</Text>
          <Text style={styles.infoDetails}>
            {new Date(contestDetails?.endTime).toLocaleString()}
          </Text>
        </View>
        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>Duration</Text>
          <Text style={styles.infoDetails}>
            {Math.round(contestDetails?.contestDuration / 60)} Minutes
          </Text>
        </View>
      </View>
    ),
    [contestDetails],
  );

  return (
    <View style={styles.container}>
      {headerContent}
      {infoRowContent}
    </View>
  );
};

const ContestDetailCardContainer = React.forwardRef(
  (props: ContestDetailCardProps, ref) => {
    const { contestDetails, loading, error, refetch } = props;

    if (_isEmpty(contestDetails)) {
      return null;
    }

    if (loading && _isEmpty(contestDetails))
      return (
        <ErrorView
          errorMessage="Something went wrong while fetching Contest"
          onRetry={refetch}
        />
      );

    if (error) return <Text>Something Went Wrong</Text>;

    return <ContestDetailCard ref={ref} {...props} />;
  },
);

export default React.memo(ContestDetailCardContainer);
