import { StyleSheet } from 'react-native';
import dark from '../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
    container: {
        flex:1,
        maxHeight:'auto',
        justifyContent: 'flex-start',
    },
    headerContainer: {
        flexDirection: 'row',
        paddingVertical: 10,
        marginTop:25,
        borderBottomWidth: 1,
        borderBottomColor: '#333',
        marginHorizontal:15
    },
    rowContainer: {
        flexDirection: 'row',
        paddingVertical: 10,
        marginHorizontal:15
    },
    column1: {
        flex: 1, 
        alignItems: 'flex-start',
        paddingHorizontal: 5,

    },
    column2: {
        flex: 3, 
        alignItems: 'flex-start',
        paddingHorizontal: 5,
    },
    column3: {
        alignItems: 'flex-end',
        flex: 2,
        paddingHorizontal: 5,
    },
    column4: {
        flex: 2,
        paddingHorizontal: 5,
    },
    headerLabel: {
        color: dark.colors.textDark,
        fontFamily: 'Montserrat-500',
        fontSize: 12,
    },
    rowLabel: {
        color: '#FFFFFF',
        fontSize: 14,
        fontFamily: 'Montserrat-500',
    },
    separator: {
        height: 1,
        marginHorizontal:13,
        backgroundColor: '#333',
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyText: {
        color: '#FFFFFF',
        fontSize: 16,
    },
});

export default styles;
