import React, { useCallback, useState } from 'react';
import { ScrollView } from 'react-native';

import ExpandableSection from 'modules/contest/components/ExpandableSection';
import styles from '../CompactContestDetail/CompactContestDetail.style';

interface CompactContestDetailsTabContentProps {
  contestDetails: any;
}

const CompactContestDetailsTabContent = ({
  contestDetails,
}: CompactContestDetailsTabContentProps) => {
  const [expandedSections, setExpandedSections] = useState({
    about: false,
    requirements: false,
    instructions: false,
  });

  const toggleSection = useCallback((section: string) => {
    setExpandedSections((prev: any) => ({
      ...prev,
      [section]: !prev[section],
    }));
  }, []);

  return (
    <ScrollView contentContainerStyle={styles.contestDetailContainer}>
      <ExpandableSection
        title="About Contest"
        expanded={expandedSections.about}
        onToggle={() => toggleSection('about')}
        content={contestDetails?.details?.about}
      />
      <ExpandableSection
        title="Requirements"
        expanded={expandedSections.requirements}
        onToggle={() => toggleSection('requirements')}
        content={contestDetails?.details?.requirements}
      />
      <ExpandableSection
        title="Instructions"
        expanded={expandedSections.instructions}
        onToggle={() => toggleSection('instructions')}
        content={contestDetails?.details?.instructions}
      />
    </ScrollView>
  );
};

export default React.memo(CompactContestDetailsTabContent);
