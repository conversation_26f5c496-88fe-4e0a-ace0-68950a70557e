import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    backgroundColor: dark.colors.primary,
    borderRadius: 10,
    marginHorizontal: 10,
  },
  gradientBox: {
    marginTop: 24,
    marginBottom: 20,
    marginHorizontal: 13,
    borderRadius: 15,
  },
  rankCard: {
    flexDirection: 'row',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    padding: 10,
    paddingVertical: 20,
    gap: 12,
    marginLeft: 16,
  },
  userImage: {
    width: 70,
    flexShrink: 1,
    height: 70,
    shadowColor: dark.colors.primary,
    shadowOpacity: 0.3,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 4 },
    borderRadius: 8,
    overflow: 'hidden',
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  statDisplay: {
    alignContent: 'center',
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'row',
    gap: 4,
  },
  rankInfo: {
    flex: 1,
  },
  rankText: {
    fontSize: 32,
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-700',
  },
  statsRow: {
    width: '80%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 5,
  },
  stat: {
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-500',
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 20,
  },
  shareButton: {
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderColor: dark.colors.textLight,
    borderWidth: 1.5,
    borderRadius: 20,
  },
  shareText: {
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-500',
    fontSize: 12,
  },
  footerText: {
    textAlign: 'center',
    color: dark.colors.secondary,
    fontFamily: 'Montserrat-500',
    marginTop: 30,
    marginBottom: 100,
  },
});

export default styles;
