import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Modal, ScrollView, Switch, Text, TextInput, View } from 'react-native';
import Entypo from '@expo/vector-icons/Entypo';
import { Picker } from '@react-native-picker/picker';
import _size from 'lodash/size';
import _some from 'lodash/some';
import _get from 'lodash/get';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import useSendOTP from 'core/hooks/useSendOTP';
import dark from 'core/constants/themes/dark';
import EmailVerificationFlow from '@/src/components/shared/EmailVerificationFlow';
import PrimaryButton from '@/src/components/atoms/PrimaryButton';
import _map from 'lodash/map';

import { validateEmailField } from 'modules/contest/utils/formValidator';
import { FIELD_TYPES } from 'modules/contest/constants';
import formModalStyles from './FormModal.style';

interface FormModalProps {
  modalVisible: boolean;
  setModalVisible: (visible: boolean) => void;
  fields: any[];
  onSubmit: (formData: any) => void;
  isLoading: boolean;
}

const FormModal = ({
  modalVisible,
  setModalVisible,
  fields,
  onSubmit,
  isLoading,
}: FormModalProps) => {
  const [formData, setFormData] = useState({});
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [focusedField, setFocusedField] = useState(null);
  const firstInputRef = useRef(null);

  const { sendOTPToEmail, isSendingOTP } = useSendOTP();

  useEffect(() => {
    if (modalVisible && firstInputRef.current) {
      firstInputRef.current?.focus();
    }
  }, [modalVisible]);

  const handleInputChange = useCallback((name: string, value: any) => {
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  }, []);

  const onChangeEmail = useCallback(() => {
    setIsOtpSent(false);
    setIsEmailVerified(false);
  }, []);

  const isSendOTPDisabled = isSendingOTP || isOtpSent;

  const sendOTP = useCallback(async () => {
    let isValidForm = true;
    let email = '';

    if (isSendOTPDisabled) {
      return;
    }

    fields.forEach((field) => {
      if (field.type === FIELD_TYPES.EMAIL) {
        const { isValid, errors } = validateEmailField({
          field,
          value: formData[field.name],
          suffix: field?.validation?.emailSuffix,
          suffixes: field?.validation?.emailSuffixes,
        });
        email = formData[field.name];
        isValidForm = isValidForm && isValid;

        if (_size(errors) > 0) {
          showToast({
            type: TOAST_TYPE.ERROR,
            description: `${errors?.[0]}`,
          });
        }
      }

      if (field.required && !formData[field.name]) {
        isValidForm = false;
        showToast({
          type: TOAST_TYPE.ERROR,
          description: `${field.label} is required`,
        });
      }
    });

    if (isValidForm) {
      if (!isSendingOTP) {
        const otpSent = await sendOTPToEmail({ email });
        setIsOtpSent(otpSent);
        return otpSent;
      }
    }
    return false;
  }, [sendOTPToEmail, isSendingOTP, fields, formData, isSendOTPDisabled]);

  const renderField = useCallback(
    (field, index) => {
      const needManualVerification = field?.validation?.needManualVerification;

      const inputProps = {
        ref: index === 0 ? firstInputRef : null,
        style: [
          formModalStyles.inputField,
          focusedField === field.name && formModalStyles.focusedInputField,
        ],
        onFocus: () => setFocusedField(field.name),
        onBlur: () => setFocusedField(null),
        onChangeText: (value) => handleInputChange(field.name, value),
        placeholder: field.label,
        min: field.min,
        max: field.max,
        editable: true,
      };

      let inputElement;
      switch (field.type) {
        case FIELD_TYPES.EMAIL:
          inputElement = (
            <>
              <View style={formModalStyles.emailContainer}>
                <TextInput
                  {...inputProps}
                  style={[inputProps.style, formModalStyles.emailInput]}
                  placeholderTextColor="grey"
                  keyboardType="email-address"
                  editable={!isOtpSent && !isSendingOTP}
                />
              </View>

              {needManualVerification && isOtpSent && (
                <EmailVerificationFlow
                  onVerificationComplete={(isValid) =>
                    setIsEmailVerified(isValid)
                  }
                  sendOTPToEmail={sendOTP}
                  isSendingOTP={isSendingOTP}
                  isOtpSent={isOtpSent}
                  onChangeEmail={onChangeEmail}
                />
              )}
            </>
          );
          break;
        case FIELD_TYPES.TEXT:
          inputElement = (
            <TextInput {...inputProps} placeholderTextColor="grey" />
          );
          break;
        case FIELD_TYPES.NUMBER:
          inputElement = <TextInput {...inputProps} keyboardType="numeric" />;
          break;
        case FIELD_TYPES.SINGLE_SELECT:
          inputElement = (
            <Picker
              selectedValue={formData[field.name]}
              onValueChange={(value) => handleInputChange(field.name, value)}
            >
              {field.options.map((option, idx) => (
                <Picker.Item key={idx} label={option} value={option} />
              ))}
            </Picker>
          );
          break;
        case FIELD_TYPES.CHECKBOX:
          inputElement = (
            <Switch
              value={formData[field.name] || false}
              onValueChange={(value) => handleInputChange(field.name, value)}
            />
          );
          break;
        default:
          inputElement = <TextInput {...inputProps} />;
      }

      return (
        <View key={index} style={formModalStyles.inputContainer}>
          <Text style={formModalStyles.inputLabel}>
            {field.label}{' '}
            {field.required && <Text style={formModalStyles.required}>*</Text>}
          </Text>
          {inputElement}
        </View>
      );
    },
    [
      onChangeEmail,
      focusedField,
      handleInputChange,
      isOtpSent,
      isSendingOTP,
      sendOTP,
      formData,
    ],
  );

  const isEmailVerificationRequired = useMemo(
    () =>
      _some(fields, (field) => {
        const isEmailField = field?.type === FIELD_TYPES.EMAIL;
        const needManualVerification = _get(
          field,
          ['validation', 'needManualVerification'],
          false,
        );
        return isEmailField && needManualVerification;
      }),
    [fields],
  );

  const isSubmitDisabled = useMemo(
    () => isLoading || (isEmailVerificationRequired && !isEmailVerified),
    [isLoading, isEmailVerified, isEmailVerificationRequired],
  );

  const handleSubmit = useCallback(() => {
    let isValidForm = true;
    const allErrors = [];

    if (isSubmitDisabled) {
      return;
    }

    fields.forEach((field) => {
      if (field.type === FIELD_TYPES.EMAIL) {
        const { isValid, errors } = validateEmailField({
          field,
          value: formData[field.name],
          suffix: field?.validation?.emailSuffix,
          suffixes: field?.validation?.emailSuffixes,
        });

        isValidForm = isValidForm && isValid;

        if (_size(errors) > 0) {
          allErrors.push(errors?.[0]);
          showToast({
            type: TOAST_TYPE.ERROR,
            description: `${errors?.[0]}`,
          });
        }
      }

      if (field.required && !formData[field.name]) {
        isValidForm = false;
        showToast({
          type: TOAST_TYPE.ERROR,
          description: `${field.label} is required`,
        });
      }
    });

    if (isValidForm && !isSubmitDisabled) {
      onSubmit?.(formData);
    }
    setIsEmailVerified(false);
    setIsOtpSent(false);
  }, [fields, formData, onSubmit, isSubmitDisabled]);

  const renderButton = () => {
    if (isEmailVerificationRequired && !isEmailVerified) {
      return (
        <PrimaryButton
          buttonStyle={[
            formModalStyles.submitButton,
            isSendOTPDisabled && formModalStyles.disabledButton,
          ]}
          onPress={sendOTP}
          disabled={isSendOTPDisabled}
          label={isSendingOTP ? 'Sending...' : 'Send OTP'}
        />
      );
    }
    return (
      <PrimaryButton
        buttonStyle={[
          formModalStyles.submitButton,
          isSubmitDisabled && formModalStyles.disabledButton,
        ]}
        onPress={handleSubmit}
        disabled={isSubmitDisabled}
        label={isLoading ? 'Submitting...' : 'Submit'}
      />
    );
  };

  return (
    <Modal animationType="fade" transparent visible={modalVisible}>
      <View style={formModalStyles.modalContainer}>
        <View style={formModalStyles.modalContent}>
          <View style={{ alignItems: 'flex-end', paddingBottom: 12 }}>
            <Entypo
              name="cross"
              size={22}
              color={dark.colors.textDark}
              onPress={() => setModalVisible(false)}
            />
          </View>
          <ScrollView>{_map(fields, renderField)}</ScrollView>
          <View style={formModalStyles.buttonContainer}>{renderButton()}</View>
        </View>
      </View>
    </Modal>
  );
};

export default React.memo(FormModal);
