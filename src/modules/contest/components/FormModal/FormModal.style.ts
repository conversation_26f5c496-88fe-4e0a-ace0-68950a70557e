import { Dimensions, StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const formModalStyles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalContent: {
    backgroundColor: dark.colors.card,
    borderRadius: 20,
    width: Math.min(Dimensions.get('window').width * 0.9, 400),
    padding: 20,
  },
  disabledButton: {
    opacity: 0.3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 35,
  },
  headerText: {
    fontSize: 18,
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-500',
  },
  closeIcon: {
    color: dark.colors.textLight,
    fontSize: 18,
  },
  inputLabelBox: {
    flex: 1,
    display: 'flex',
    flexDirection: 'row',
    gap: 10,
    marginRight: 10,
  },
  inputContainer: {
    marginBottom: 25,
  },
  required: {
    color: dark.colors.errorDark,
  },
  inputLabel: {
    fontSize: 14,
    color: dark.colors.textLight,
    marginBottom: 10,
    fontFamily: 'Montserrat-500',
  },
  inputLabelMandatory: {
    fontSize: 16,
    color: dark.colors.error,
    marginBottom: 5,
    fontWeight: '700',
  },
  inputField: {
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    borderRadius: 10,
    outlineStyle: 'none',
    padding: 12,
    fontSize: 16,
    color: dark.colors.textLight,
    backgroundColor: '#333',
  },
  otpButton: {
    width: '100%',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    marginTop: 10,
  },
  otpButtonText: {
    fontFamily: 'Montserrat-400',
    fontSize: 13,
    color: dark.colors.secondary,
    textAlign: 'left',
  },
  focusedInputField: {
    borderWidth: 1,
    outlineStyle: 'none',
    borderColor: dark.colors.secondary,
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    color: dark.colors.textLight,
    backgroundColor: 'transparent',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 10,
    width: '100%',
    marginRight: 0,
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  cancelButton: {
    backgroundColor: 'transparent',
    padding: 12,
    borderRadius: 10,
    flex: 1,
    alignItems: 'center',
  },
  submitButton: {
    width: 150,
    height: 40,
    backgroundColor: dark.colors.secondary,
    padding: 12,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButtonText: {
    color: dark.colors.secondary,
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
  submitButtonText: {
    color: dark.colors.card,
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },

  changeEmailButton: {
    paddingVertical: 8,
    borderRadius: 4,
  },
  changeEmailText: {
    color: dark.colors.secondary,
    fontSize: 14,
    fontFamily: 'Montserrat-500',
  },
  resendOtpText: {
    color: dark.colors.secondary,
    fontSize: 14,
    textDecorationLine: 'underline',
  },
});

export default formModalStyles;
