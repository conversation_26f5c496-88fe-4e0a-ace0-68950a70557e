import { Platform, StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from 'core/constants/themes/dark';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      backgroundColor: '#243619',
      borderRadius: 14,
      marginHorizontal: 0,
      alignItems: 'center',
      width: isCompactMode ? '48%' : 180,
      height: 'auto',
      elevation: 10, // For Android shadow
      overflow: 'hidden', // Ensures the inner shadow is clipped
      position: 'relative', // Needed for inner shadow positioning
    },
    imageContainer: {
      borderRadius: 12,
      paddingTop: 20,
      paddingBottom: 14,
      alignItems: 'center',
      width: isCompactMode ? '100%' : 180,
      height: Platform.select({
        android: 'auto',
        web: 'auto',
      }),
      flex: 1,
      overflow: 'hidden',
      zindex: 1,
    },

    registeredBadge: {
      backgroundColor: '#00821C',
      paddingHorizontal: isCompactMode ? 6 : 12,
      paddingVertical: isCompactMode ? 3 : 6,
      marginBottom: isCompactMode ? 10 : 20,
      borderTopRightRadius: 20,
      borderBottomRightRadius: 20,
      position: 'absolute',
      top: isCompactMode ? 15 : 20,
      left: 0,
    },
    registeredText: {
      color: '#FFFFFF',
      fontSize: isCompactMode ? 10 : 12,
      fontFamily: 'Montserrat-600',
    },
    registerEndsBadge: {
      backgroundColor: '#3B3B3B',
      paddingHorizontal: isCompactMode ? 6 : 12,
      paddingVertical: isCompactMode ? 3 : 6,
      marginBottom: isCompactMode ? 10 : 20,
      borderTopRightRadius: 20,
      borderBottomRightRadius: 20,
      position: 'absolute',
      top: isCompactMode ? 15 : 20,
      left: 0,
    },
    registerEndsText: {
      color: dark.colors.textDark,
      fontFamily: 'Montserrat-600',
      fontSize: isCompactMode ? 10 : 12,
    },
    endedText: {
      color: 'red',
      fontFamily: 'Montserrat-600',
      fontSize: isCompactMode ? 10 : 12,
    },
    iconContainer: {
      marginTop: 10,
      width: isCompactMode ? 65 : 150,
      height: isCompactMode ? 60 : 90,
      borderRadius: 10,
      justifyContent: 'center',
      alignItems: 'center',
      // marginTop: isCompactMode ? 15 : 30,
    },
    logoStyle: {
      width: isCompactMode ? 65 : 80,
      height: isCompactMode ? 80 : 90,
    },
    title: {
      color: '#FFFFFF',
      fontSize: isCompactMode ? 12 : 15,
      fontFamily: 'Montserrat-500',
      paddingHorizontal: 10,
      overflow: 'hidden',
      marginTop: isCompactMode ? 7 : 20,
      alignItems: 'center',
      textAlign: 'center',
      lineHeight: 15,
      marginBottom: isCompactMode ? 2 : 5,
    },
    hostedBy: {
      color: dark.colors.textDark,
      fontSize: isCompactMode ? 9 : 12,
      fontFamily: 'Montserrat-600',
      marginHorizontal: 10,
      alignItems: 'center',
      textAlign: 'center',
      marginBottom: 6,
      lineHeight: 11,
    },
    date: {
      marginTop: 20,
      color: '#FFFFFF',
      fontSize: isCompactMode ? 14 : 12,
      fontFamily: 'Montserrat-700',
      marginHorizontal: 10,
    },
    time: {
      marginTop: 5,
      color: '#FFFFFF',
      fontSize: isCompactMode ? 10 : 12,
      fontFamily: 'Montserrat-600',
      marginHorizontal: 10,
    },
    statusContainer: {
      marginTop: 10,
      width: '100%',
      height: 20,
      backgroundColor: '#FBD43F',
      opacity: 0.9,
      alignItems: 'center',
      justifyContent: 'center',
    },
    statusText: {
      color: '#3C5A29',
      fontSize: isCompactMode ? 7 : 9,
      fontFamily: 'Montserrat-700',
      fontWeight: 'bold',
    },
    statusLiveContainer: {
      marginTop: 10,
      width: '70%',
      height: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 12,
    },
    liveBadge: {
      position: 'absolute',
      top: 12,
      right: 12,
      height: 13,
      width: 28,
      backgroundColor: '#F84E4E',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 12,
    },
    liveBadgeText: {
      color: 'white',
      fontFamily: 'Montserrat-500',
      paddingHorizontal: 8,
      fontSize: isCompactMode ? 2 : 5,
      letterSpacing: 1,
    },
    statusLiveText: {
      color: 'white',
      fontSize: 10,
      fontFamily: 'Montserrat-800',
      fontWeight: 'bold',
      letterSpacing: 0.7,
    },

    statusIcon: {
      marginRight: 4,
      width: 12,
      height: 12,
    },

    statusRegistered: {
      color: '#BABABA',
      fontSize: isCompactMode ? 12 : 10,
      fontFamily: 'Montserrat-700',
      fontWeight: 'bold',
    },

    liveContainer: {
      marginTop: 15,
      width: '100%',
      height: 20,
      backgroundColor: '#FBD43F',
      opacity: 0.9,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 12,
    },
  });

const useShowdownCardStyles = () => {
  const { isMobile } = useMediaQuery();
  return useMemo(() => createStyles(isMobile), [isMobile]);
};

export default useShowdownCardStyles;
