import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Platform,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _isNil from 'lodash/isNil';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import useContestListTabStyles from '../ContestListsTab/ContestListsTab.style';
import ContestCardShimmer from '../../shimmers/ContestCardShimmer';
import dark from '../../../../core/constants/themes/dark';
import { SHOWDOWN_STATUS } from '../../../showdown/constants';
import useGetShowdownsByStatus from '../../hooks/queries/useGetShowdownStatusQuery';
import ShowdownCard from './ShowdownCard';

const TAB_VS_STATUS = {
  [SHOWDOWN_STATUS.REGISTRATION_OPEN]: [
    SHOWDOWN_STATUS.UPCOMING,
    SHOWDOWN_STATUS.REGISTRATION_OPEN,
    SHOWDOWN_STATUS.FICTURES_CREATED,
  ],
  [SHOWDOWN_STATUS.ENDED]: [SHOWDOWN_STATUS.ENDED],
};

const ShowdownListTabs = () => {
  const { isMobile: isCompactMode, isTablet } = useMediaQuery();
  const styles = useContestListTabStyles();
  const [contests, setContests] = useState([]);

  // Query both upcoming and completed showdowns
  const { showdownDetail, error, loading, refetch } = useGetShowdownsByStatus({
    statuses: [
      SHOWDOWN_STATUS.UPCOMING,
      SHOWDOWN_STATUS.REGISTRATION_OPEN,
      SHOWDOWN_STATUS.ENDED,
      SHOWDOWN_STATUS.LIVE,
      SHOWDOWN_STATUS.FICTURES_CREATED,
    ],
  });

  const contestStyles = StyleSheet.create({
    featuredContests: {
      width: isCompactMode ? '90%' : '80%',
      justifyContent: 'center',
      marginLeft: isCompactMode ? 18 : 0,
      marginRight: isCompactMode ? 18 : 0,
      alignSelf: isCompactMode ? 'center' : '',
      marginTop: 16,
    },
    container: {
      flex: 1,
      borderColor: 'transparent',
      borderWidth: 0,
    },
  });

  const isNativeDevice = Platform.OS === 'ios' || Platform.OS === 'android';
  const numOfColumns = isCompactMode ? 2 : isTablet ? 3 : 4;

  useEffect(() => {
    if (showdownDetail?.showdowns) {
      setContests(showdownDetail?.showdowns);
    }
  }, [showdownDetail]);

  const renderItem = useCallback(
    ({ item }) => (
      <ShowdownCard
        contestDetails={item}
        key={item._id}
        isCompleted={item.status === SHOWDOWN_STATUS.ENDED}
        contestId={item._id}
        registrationEndTime={item.registrationEndTime}
        title={item.name}
        date={new Date(item.startTime).toLocaleString()}
        status={item.status}
        registered={!!item.currentUserParticipation}
        color={item.color}
        hostedBy={item?.hostedBy}
      />
    ),
    [],
  );

  const noContestText = useMemo(
    () => (
      <View
        style={{
          height: '80%',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <FontAwesome5 name="trophy" size={48} color={dark.colors.tertiary} />
        <Text style={styles.noContestText}>No contests to show</Text>
      </View>
    ),
    [],
  );

  const renderContent = () => {
    if (loading || _isNil(showdownDetail)) {
      return <ContestCardShimmer />;
    }
    if (error) {
      return (
        <Text style={styles.tabText}>
          Error loading contests. Please try again.
        </Text>
      );
    }
    if (contests.length === 0) {
      return noContestText;
    }
    return (
      <FlatList
        key={`showdown-list-${numOfColumns}`}
        data={showdownDetail?.showdowns}
        keyExtractor={(item, index) => `${item._id.toString()}-${index}`}
        renderItem={renderItem}
        numColumns={numOfColumns}
        columnWrapperStyle={styles.columnWrapper}
        showsVerticalScrollIndicator={false}
        ListFooterComponent={
          loading ? <ActivityIndicator size="small" color="#FFFFFF" /> : null
        }
        style={{ marginHorizontal: isCompactMode ? '5%' : 0 }}
      />
    );
  };

  return (
    <View style={contestStyles.container}>
      {/* {showdownDetail?.showdowns?.length > 0 ? (
        <View
          style={[
            contestStyles.featuredShowdown,
            isNativeDevice && { width: 'auto' },
          ]}
        >
          <ShowdownBanner showdown={showdownDetail?.showdowns?.[0]} />
        </View>
      ) : null} */}
      <View style={styles.container}>{renderContent()}</View>
    </View>
  );
};

export default React.memo(ShowdownListTabs);
