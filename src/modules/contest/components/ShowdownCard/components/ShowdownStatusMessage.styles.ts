import { StyleSheet } from 'react-native';

import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';

const getShowDownStatusStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    statusContainer: {
      marginTop: 10,
      width: '100%',
      height: 20,
      backgroundColor: '#FBD43F',
      opacity: 0.9,
      alignItems: 'center',
      justifyContent: 'center',
    },
    statusText: {
      color: '#3C5A29',
      fontSize: isCompactMode ? 7 : 9,
      fontFamily: 'Montserrat-700',
      fontWeight: 'bold',
    },
    statusLiveContainer: {
      marginTop: 10,
      width: '70%',
      height: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 12,
    },
    liveBadge: {
      position: 'absolute',
      top: 12,
      right: 12,
      height: 13,
      width: 28,
      backgroundColor: '#F84E4E',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 12,
    },
    liveBadgeText: {
      color: 'white',
      fontFamily: 'Montserrat-500',
      paddingHorizontal: 8,
      fontSize: isCompactMode ? 2 : 5,
      letterSpacing: 1,
    },
    statusLiveText: {
      color: 'white',
      fontSize: 10,
      fontFamily: 'Montserrat-800',
      fontWeight: 'bold',
      letterSpacing: 0.7,
    },

    statusIcon: {
      marginRight: 4,
      width: 12,
      height: 12,
    },

    statusRegistered: {
      color: '#BABABA',
      fontSize: isCompactMode ? 12 : 10,
      fontFamily: 'Montserrat-700',
      fontWeight: 'bold',
    },

    liveContainer: {
      marginTop: 15,
      width: '100%',
      height: 20,
      backgroundColor: '#FBD43F',
      opacity: 0.9,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 12,
    },
  });

const useShowdownStatusStyles = () => {
  const { isMobile } = useMediaQuery();
  return useMemo(() => getShowDownStatusStyles(isMobile), [isMobile]);
};

export default useShowdownStatusStyles;
