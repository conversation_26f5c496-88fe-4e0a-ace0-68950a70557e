import _isArray from 'lodash/isArray';
import _isEmpty from 'lodash/isEmpty';

interface ValidateEmailFieldProps {
  field: any;
  value: string;
  suffix?: string;
  suffixes?: string[];
}

export const validateEmailField = ({
  field,
  value,
  suffix = '',
  suffixes = [],
}: ValidateEmailFieldProps) => {
  // Initialize an empty errors array
  const errors = [];

  // Check if the field is required and the value is empty
  if (field.required && (!value || value.trim() === '')) {
    errors.push(`${field.label} is required`);
  }

  // If value is not empty, perform email validation
  if (value && value.trim() !== '') {
    const basicEmailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!basicEmailRegex.test(value)) {
      errors.push(`${field.label} must be a valid email address`);
    }

    // Check for a single suffix if provided

    if (_isArray(suffixes)) {
      const hasValidSuffix = suffixes.some((s) => value.endsWith(s));

      if (!hasValidSuffix) {
        errors.push(`Your Email is not allowed.`);
      }
    }
    if (!_isEmpty(suffix) && !value.endsWith(suffix)) {
      errors.push(`Your Email is not allowed.`);
    }

    // If a custom regex is provided in the validation, use it
    if (field.validation && field.validation.regex) {
      const customRegex = new RegExp(field.validation.regex);
      if (!customRegex.test(value)) {
        errors.push(`${field.label} does not match the required format`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors: errors,
  };
};
