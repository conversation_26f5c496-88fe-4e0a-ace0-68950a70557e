import _isNil from 'lodash/isNil';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import _head from 'lodash/head';

export const getTimeSpentByUser = ({
  contest,
  participantSubmission,
}: {
  contest: any;
  participantSubmission: any;
}) => {
  if (_isEmpty(contest) || _isEmpty(participantSubmission)) {
    return 0;
  }

  const {
    startTime: contestStartTime,
    endTime: contestEndTime,
    contestDuration,
  } = contest;
  const contestStartTimeStamp = new Date(contestStartTime).getTime();
  const contestEndTimeStamp = new Date(contestEndTime).getTime();

  const {
    startTime: userStartTime,
    lastSubmissionTime,
    correctSubmission: correctSubmissionCount = 0,
  } = participantSubmission;

  const hasUserSubmittedAllTheAnswers =
    correctSubmissionCount === _size(contest.questions);

  if (_isNil(lastSubmissionTime)) {
    return 0;
  }

  const userStartTimeStamp = new Date(
    userStartTime ?? contestStartTime,
  ).getTime();
  const lastSubmissionTimeStamp = new Date(lastSubmissionTime).getTime();

  const startTime = Math.max(contestStartTimeStamp, userStartTimeStamp);

  const effectiveLastSubmissionTimeStamp = Math.min(
    lastSubmissionTimeStamp,
    contestEndTimeStamp,
  );

  const endTime = hasUserSubmittedAllTheAnswers
    ? Math.min(effectiveLastSubmissionTimeStamp, contestEndTimeStamp)
    : Math.min(
        startTime + contestDuration * 1000,
        Math.max(effectiveLastSubmissionTimeStamp, contestEndTimeStamp),
      );

  if (lastSubmissionTimeStamp > endTime && startTime > endTime) {
    return Math.min(
      lastSubmissionTimeStamp - startTime,
      contestDuration * 1000,
    );
  }

  return endTime > startTime ? endTime - startTime : 0;
};

export const getStartTimeOfContestForUser = ({
  contest,
  userSubmission,
}: {
  contest: any;
  userSubmission: any;
}) => {
  if (_isNil(userSubmission)) {
    return null;
  }

  const { startTime: startTimeForUser, submissions } = userSubmission;
  if (!_isNil(startTimeForUser)) {
    return new Date(startTimeForUser).getTime();
  }

  const { startTime: contestStartTime } = contest;
  const firstQuestionSubmissionTime = _head(submissions)?.submissionTime;

  return Math.max(
    new Date(firstQuestionSubmissionTime).getTime() - 1000,
    new Date(contestStartTime).getTime(),
  );
};
