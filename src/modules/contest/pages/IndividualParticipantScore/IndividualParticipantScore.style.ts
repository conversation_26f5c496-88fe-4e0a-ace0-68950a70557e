import { StyleSheet } from 'react-native';

import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout';
import dark from 'core/constants/themes/dark';

export default StyleSheet.create({
  container: {
    maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
    flex: 1,
    gap: 20,
  },
  gradientBox: {
    width: 55,
    height: 55,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    gap: 15,
    marginTop: 10,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  congratsText: {
    fontSize: 36,
    textAlign: 'center',
  },
  contestStatContainer: {
    gap: 16,
  },
  statValue: {
    fontSize: 48,
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-700',
  },
  statLabel: {
    fontSize: 14,
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-700',
  },
  title: {
    color: dark.colors.textLight,
    fontSize: 18,
    fontFamily: 'Montserrat-700',
  },
  liveDetail: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10,
  },
  hostDetail: {
    display: 'flex',
    flexDirection: 'row',
    gap: 5,
    marginBottom: 20,
  },
  hostedBy: {
    color: dark.colors.textDark,
    fontSize: 14,
    fontFamily: 'Montserrat-500',
  },
  userDetail: {
    marginTop: 20,
    display: 'flex',
    flexDirection: 'row',
    gap: 20,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  userName: {
    fontSize: 18,
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-500',
  },
  overViewText: {
    marginTop: 20,
    fontSize: 20,
    color: dark.colors.errorDark,
    fontFamily: 'Montserrat-500',
  },
  statsContainer: {
    marginBottom: 30,
    flexDirection: 'row',
    gap: 48,
  },
  errorContainer: {
    alignItems: 'center',
    gap: 24,
  },
  errorMessage: {
    fontSize: 24,
    color: dark.colors.textLight,
  },
});
