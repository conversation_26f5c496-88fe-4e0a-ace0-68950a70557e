import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import React, { useCallback, useEffect, useRef } from 'react';
import { Text, View } from 'react-native';
import ErrorView from 'atoms/ErrorView';
import Loading from 'atoms/Loading';
import { getFormattedTimeWithMS } from 'core/utils/general';
import SecondaryButton from 'atoms/SecondaryButton';
import { useRouter } from 'expo-router';
import UserImage from 'atoms/UserImage';
import userReader from 'core/readers/userReader';
import dark from 'core/constants/themes/dark';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import LinearGradient from 'atoms/LinearGradient';
import contestReader from 'core/readers/contestReader';
import styles from './IndividualParticipantScore.style';
import ScoreBreakdown from '../ContestCompletionPage/components/ScoreBreakdown';
import useContestDetails from '../../hooks/useContestDetails';
import useGetIndividualParticipantScoreInContest from '../../hooks/queries/useGetIndividualParticipantScore';
import { getTimeSpentByUser } from '../../utils/contest';

interface IndividualParticipantScoreProps {
  contestId: string;
  userId: string;
}

const IndividualParticipantScore = ({
  contestId,
  userId,
}: IndividualParticipantScoreProps) => {
  const {
    loading,
    error,
    refetch,
    contestDetails = {},
  } = useContestDetails({ contestId });

  const router = useRouter();
  const { userSubmission } = useGetIndividualParticipantScoreInContest({
    contestId,
    userId,
  });

  const endTime = contestReader.endTime(contestDetails);
  const questions = contestReader.questions(contestDetails);

  const { submissions, totalScore } = userSubmission ?? EMPTY_OBJECT;

  const totalNumberOfSubmissions = _size(submissions);

  const endTimeInMs = contestReader.endTimeMs(contestDetails);

  const timeTaken = getTimeSpentByUser({
    contest: contestDetails,
    participantSubmission: userSubmission,
  });

  const formattedTimeTaken = getFormattedTimeWithMS(timeTaken);

  const renderContestStat = useCallback(
    ({ label, value }: { label: string; value: number }) => (
      <View style={styles.contestStatContainer} key={label}>
        <Text style={styles.statValue}>{value}</Text>
        <Text style={styles.statLabel}>{label}</Text>
      </View>
    ),
    [],
  );

  const stats = [
    { label: 'Question Solved', value: totalNumberOfSubmissions },
    { label: 'Total Questions', value: _size(questions) },
    { label: 'Total Score', value: totalScore },
    { label: 'Total Time Taken (min:sec:ms)', value: formattedTimeTaken },
  ];

  const onPressGoHome = useCallback(() => router.push(`/home`), [router]);
  const justEnded = getCurrentTimeWithOffset() - endTimeInMs < 10000;

  const refetchRef = useRef(refetch);
  refetchRef.current = refetch;

  useEffect(() => {
    if (justEnded) {
      refetchRef.current();
    }
  }, [justEnded]);

  if (endTimeInMs >= getCurrentTimeWithOffset()) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorMessage}>
          Contest is Still Live so can not show individual Player score
        </Text>
        <SecondaryButton
          onPress={onPressGoHome}
          label="Go To Home"
          radius={8}
          buttonStyle={{ width: 200 }}
        />
      </View>
    );
  }

  if (error) {
    return (
      <ErrorView errorMessage="Something went wrong while loading your submission. Please try again later." />
    );
  }

  if (loading || _isEmpty(userSubmission)) {
    return <Loading label={"Loading User's Score"} />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View>
          <LinearGradient
            colors={dark.colors.contestLogoBgGradient}
            style={styles.gradientBox}
          >
            <View style={styles.iconContainer}>
              <Text style={styles.title}>🏆</Text>
            </View>
          </LinearGradient>
        </View>
        <View>
          <View style={styles.liveDetail}>
            <Text style={styles.title}>{contestDetails.name}</Text>
          </View>
          <View style={styles.hostDetail}>
            <Text style={styles.hostedBy}>{contestDetails.description}</Text>
          </View>
        </View>
      </View>
      <View style={styles.userDetail}>
        <UserImage user={userSubmission?.user} size={80} />
        <Text style={styles.userName}>
          {userReader.username(userSubmission?.user)}
        </Text>
      </View>
      <Text style={styles.overViewText}>Score Overview</Text>
      <View style={styles.statsContainer}>{stats.map(renderContestStat)}</View>
      <ScoreBreakdown questionList={questions} scoreList={userSubmission} />
    </View>
  );
};

export default React.memo(IndividualParticipantScore);
