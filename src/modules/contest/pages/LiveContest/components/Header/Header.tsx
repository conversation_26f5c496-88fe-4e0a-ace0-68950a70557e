import React, { useCallback, useEffect, useRef, useState } from 'react';

import { View } from 'react-native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { Text } from '@rneui/themed';
import _size from 'lodash/size';

import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import contestReader from 'core/readers/contestReader';
import styles from './Header.style';

interface HeaderProps {
  contest: any;
  currentScore: number;
  startTimeForUser: number;
  isVirtualContest?: boolean;
}

const Header = ({
  contest,
  currentScore,
  startTimeForUser,
  isVirtualContest = false,
}: HeaderProps) => {
  const questions = contestReader.questions(contest);
  const endTimeInMs = contestReader.endTimeMs(contest);
  const contestDuration = contestReader.contestDuration(contest) ?? 0;

  const expectedEndTimeForUser =
    new Date(startTimeForUser).getTime() + contestDuration * 1000;

  const endDateTime = isVirtualContest
    ? expectedEndTimeForUser
    : Math.min(endTimeInMs, expectedEndTimeForUser);

  const currentTime = getCurrentTimeWithOffset();

  const [timeLeft, setTimeLeft] = useState(endDateTime - currentTime);

  const getFormattedTime = useCallback(() => {
    const timeLeftInSeconds = Math.floor(timeLeft / 1000);

    const minutes = Math.floor(timeLeftInSeconds / 60);
    const remainingSeconds = timeLeftInSeconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  }, [timeLeft]);

  const renderScore = useCallback(
    () => (
      <Text style={styles.playerScore}>
        {currentScore}/{_size(questions)}
      </Text>
    ),
    [currentScore, questions],
  );

  const intervalRef = useRef(null);

  useEffect(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    intervalRef.current = setInterval(() => {
      const currentTimeStamp = getCurrentTimeWithOffset();
      setTimeLeft(Math.max(0, endDateTime - currentTimeStamp));
    }, 1000);

    return () => clearInterval(intervalRef?.current);
  }, [endDateTime]);

  return (
    <View style={styles.container}>
      <View style={styles.timerBox}>
        <MaterialIcons name="timer" color="white" size={20} />
        <Text style={styles.timerText}>Time Left: {getFormattedTime()}</Text>
      </View>
      {renderScore()}
    </View>
  );
};

export default React.memo(Header);
