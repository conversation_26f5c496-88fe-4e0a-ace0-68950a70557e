import { StyleSheet } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Dark.colors.background,
  },
  mainContainerWeb: {
    justifyContent: 'center',
    gap: 24,
  },
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
    minWidth: 300,
    alignItems: 'center',
    justifyContent: 'space-evenly',
    backgroundColor: Dark.colors.background,
  },
  webContainer: {
    flexDirection: 'column',
    width: '100%',
    height: '100%',
    alignItems: 'center',
    backgroundColor: Dark.colors.background,
    justifyContent: 'center',
    gap: 24,
  },
  mobileHeader: {
    width: '100%',
    alignItems: 'center',
  },
  timerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  timerContentContainer: {
    width: '100%',
    minHeight: 300,
  },
  question: {
    flex: 1,
    width: '90%',
    maxHeight: 300,
    maxWidth: 420,
  },
  footerContainer: {
    width: '100%',
    maxWidth: 420,
  },

  // error
  errorContainer: {
    alignItems: 'center',
    gap: 24,
  },
  errorMessage: {
    textAlign: 'center',
    fontSize: 24,
    color: 'white',
  },
});

export default styles;
