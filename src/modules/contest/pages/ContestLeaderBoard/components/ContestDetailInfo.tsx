import React, { useCallback, useMemo } from 'react';
import { Text, View } from 'react-native';
import ErrorView from 'atoms/ErrorView';
import _isEmpty from 'lodash/isEmpty';
import dark from 'core/constants/themes/dark';
import LinearGradient from 'atoms/LinearGradient';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import contestReader from 'core/readers/contestReader';
import styles from '../../../components/CountdownTimer/CountdownTimer.style';
import useContestDetails from '../../../hooks/useContestDetails';
import ContestDetailCardShimmer from '../../../shimmers/ContestDetailCardShimmer';

import ContestRegistrationInfo from '../../../components/ContestRegistrationInfo';

const ContestDetailInfo = ({ contestDetails }: { contestDetails: any }) => {
  const name = contestReader.name(contestDetails);
  const description = contestReader.description(contestDetails);
  const startTime = contestReader.startTime(contestDetails);
  const endTime = contestReader.endTime(contestDetails);
  const contestDuration = contestReader.contestDuration(contestDetails);

  const isLive = useMemo(() => {
    if (!startTime || !endTime) return false;
    const now = getCurrentTimeWithOffset();
    return (
      now >= new Date(startTime).getTime() && now <= new Date(endTime).getTime()
    );
  }, [startTime, endTime]);

  const renderHeaderContent = useCallback(
    () => (
      <View style={styles.header}>
        <View>
          <LinearGradient
            colors={dark.colors.contestLogoBgGradient}
            style={styles.gradientBox}
          >
            <View style={styles.iconContainer}>
              <Text style={styles.title}>🏆</Text>
            </View>
          </LinearGradient>
        </View>
        <View>
          <View style={styles.liveDetail}>
            <Text style={styles.title}>{name}</Text>
            {isLive && (
              <View style={styles.liveContainer}>
                <Text style={styles.liveText}>Live</Text>
              </View>
            )}
          </View>
          <View style={styles.hostDetail}>
            <Text style={styles.hostedBy}>{description}</Text>
          </View>
        </View>
      </View>
    ),
    [name, isLive, description],
  );

  const renderInfoRowContent = useCallback(
    () => (
      <View style={styles.infoRow}>
        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>Contest open time</Text>
          <Text style={styles.infoDetails}>
            {new Date(startTime).toLocaleString()}
          </Text>
        </View>
        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>Contest close time</Text>
          <Text style={styles.infoDetails}>
            {new Date(endTime).toLocaleString()}
          </Text>
        </View>
        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>Duration</Text>
          <Text style={styles.infoDetails}>
            {Math.round(contestDuration / 60)} Minutes
          </Text>
        </View>
      </View>
    ),
    [contestDetails],
  );

  return (
    <View style={styles.mainContainerStyle}>
      {renderHeaderContent()}
      {renderInfoRowContent()}
      <View style={{ marginTop: 16 }}>
        <ContestRegistrationInfo contest={contestDetails} />
      </View>
    </View>
  );
};

const ContestDetailInfoContainer = ({ contestId }: { contestId: string }) => {
  const { loading, error, contestDetails } = useContestDetails({
    contestId,
  });

  if (loading) {
    return <ContestDetailCardShimmer />;
  }

  if (error || _isEmpty(contestDetails)) {
    return (
      <ErrorView errorMessage="Something went wrong while fetching Contest" />
    );
  }

  return <ContestDetailInfo contestDetails={contestDetails} />;
};

export default React.memo(ContestDetailInfoContainer);
