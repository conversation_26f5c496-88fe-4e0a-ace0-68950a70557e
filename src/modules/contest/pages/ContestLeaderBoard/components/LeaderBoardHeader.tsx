import React from 'react';
import { Text, View } from 'react-native';
import styles from '../ContestLeaderBoard.style';
import useMediaQuery from 'core/hooks/useMediaQuery';

interface LeaderBoardHeaderProps {
  headerStyle: any;
  rankColumnStyle?: any;
}

const LeaderBoardHeader = ({
  headerStyle,
  rankColumnStyle,
}: LeaderBoardHeaderProps) => {
  const { isMobile: isCompactMode } = useMediaQuery();

  return (
    <View style={headerStyle}>
      <View style={styles.rowContainer}>
        <View style={[styles.rankColumn, rankColumnStyle]}>
          <Text style={styles.headerLabelStyle}>RANK</Text>
        </View>
        <View style={styles.profileInfoColumn}>
          <Text style={styles.headerLabelStyle} numberOfLines={1}>
            MATHLETE
          </Text>
        </View>
        {!isCompactMode && (
          <View style={styles.correctSubmissionColumn}>
            <Text style={styles.headerLabelStyle}>QUESTION SOLVED</Text>
          </View>
        )}
        <View style={styles.scoreColumn}>
          <Text style={styles.headerLabelStyle}>SCORE</Text>
        </View>
        {!isCompactMode && (
          <View style={styles.timeTakenColumn}>
            <Text style={styles.headerLabelStyle}>TIME TAKEN</Text>
          </View>
        )}
      </View>
    </View>
  );
};

export default React.memo(LeaderBoardHeader);
