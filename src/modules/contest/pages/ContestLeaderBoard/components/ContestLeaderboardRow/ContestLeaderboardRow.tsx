import React, { useCallback } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

import { getFormattedTimeWithMS } from 'core/utils/general';
import useMediaQuery from 'core/hooks/useMediaQuery';

import UserImage from 'atoms/UserImage';

import { showToast, TOAST_TYPE } from 'molecules/Toast';

import { useRouter } from 'expo-router';
import contestReader from 'core/readers/contestReader';
import _get from 'lodash/get';
import styles from './ContestLeaderboardRow.style';

import { getTimeSpentByUser } from '../../../../utils/contest';
import getCurrentTimeWithOffset from '../../../../../../core/utils/getCurrentTimeWithOffset';
import userReader from '../../../../../../core/readers/userReader';

interface ContestLeaderboardRowProps {
  contest: any;
  participantSubmission: any;
}

const ContestLeaderboardRow = ({
  contest,
  participantSubmission,
}: ContestLeaderboardRowProps) => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const router = useRouter();
  const contestEndTimeMs = contestReader.endTimeMs(contest);
  const contestId = contestReader.id(contest);
  const hasContestEndedForAll = contestEndTimeMs <= getCurrentTimeWithOffset();

  const timeSpent = getTimeSpentByUser({ contest, participantSubmission });

  const participantUser = _get(participantSubmission, 'user', EMPTY_OBJECT);
  const participantUserId = userReader.id(participantUser);
  const participantUsername = userReader.username(participantUser);
  const participantUserRating = userReader.rating(participantUser);
  const participantRank = _get(participantSubmission, 'rank', EMPTY_OBJECT);
  const participantCorrectSubmission = _get(
    participantSubmission,
    'correctSubmission',
    0,
  );
  const participantScore = _get(participantSubmission, 'score', EMPTY_OBJECT);

  const handleRowPress = useCallback(() => {
    if (isCompactMode) {
      return;
    }

    if (hasContestEndedForAll) {
      router.push(
        `/contest/leaderboard/${contestId}?user=${participantUserId}`,
      );
      return;
    }
    showToast({
      type: TOAST_TYPE.INFO,
      description: 'Contest has not ended yet',
    });
  }, [
    isCompactMode,
    hasContestEndedForAll,
    router,
    contestId,
    participantUserId,
  ]);

  return (
    <TouchableOpacity
      style={[styles.rowContainer, isCompactMode && styles.compactRowContainer]}
      key={participantUserId}
      onPress={handleRowPress}
    >
      <View style={styles.rankColumn}>
        <Text style={[styles.rowLabel]}>{participantRank}</Text>
      </View>

      <View style={styles.profileInfoColumn}>
        {!isCompactMode && (
          <UserImage user={participantUser} size={isCompactMode ? 24 : 30} />
        )}
        <View
          style={[
            styles.usernameContainer,
            isCompactMode && styles.usernameContainerCompact,
          ]}
        >
          <Text
            style={[styles.rowLabel, isCompactMode && styles.rowLabelCompact]}
            numberOfLines={1}
          >
            {participantUsername}
          </Text>
          <Text style={[styles.rating, isCompactMode && styles.ratingCompact]}>
            ({participantUserRating})
          </Text>
        </View>
      </View>

      {!isCompactMode && (
        <View style={styles.correctSubmissionColumn}>
          <Text
            style={[styles.rowLabel, isCompactMode && styles.rowLabelCompact]}
          >
            {participantCorrectSubmission}
          </Text>
        </View>
      )}
      <View style={styles.scoreColumn}>
        <Text
          style={[styles.rowLabel, isCompactMode && styles.rowLabelCompact]}
        >
          {participantScore}
        </Text>
      </View>
      {!isCompactMode && (
        <View style={styles.timeTakenColumn}>
          <Text
            style={[styles.rowLabel, isCompactMode && styles.rowLabelCompact]}
          >
            {getFormattedTimeWithMS(timeSpent)}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

export default React.memo(ContestLeaderboardRow);
