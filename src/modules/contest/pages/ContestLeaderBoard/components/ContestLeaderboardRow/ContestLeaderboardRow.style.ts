import { StyleSheet } from 'react-native';
import Dark from 'core/constants/themes/dark';
import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
    flex: 1,
    maxHeight: 'auto',
    justifyContent: 'flex-start',
  },
  mainText: {
    color: Dark.colors.textDark,
    fontSize: 15,
    marginLeft: 20,
  },
  flatListContainerStyle: {
    width: '100%',
  },
  flatListContentContainerStyle: {
    paddingHorizontal: 24,
    width: '100%',
  },
  userListStyle: {
    paddingHorizontal: 24,
  },
  heading: {
    fontSize: 24,
    fontFamily: 'Montserrat-700',
    marginBottom: 16,
  },
  item: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  loadingIndicator: {
    marginTop: 50,
  },
  textContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  text: {
    color: 'white',
  },

  // header
  headerContainer: {
    marginTop: -8,
    paddingVertical: 12,
  },
  headerLabelStyle: {
    color: Dark.colors.textDark,
    fontSize: 12,
  },

  // columns
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 14,
    gap: 6,
  },
  userScoreRowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 14,
    width: '100%',
    borderWidth: 1.3,
    borderRadius: 10,
    borderColor: 'grey',
    backgroundColor: Dark.colors.tertiary,
    gap: 6,
  },
  compactRowContainer: {
    paddingVertical: 6,
  },
  rankColumn: {
    width: 36,
  },
  rowLabel: {
    color: 'white',
    fontSize: 16,
  },
  rating: {
    color: Dark.colors.textDark,
    fontSize: 16,
  },
  profileInfoColumn: {
    flex: 1,
    flexBasis: 1,
    flexDirection: 'row',
    gap: 6,
    alignItems: 'center',
  },
  scoreColumn: {
    width: 100,
    flexShrink: 1,
    alignItems: 'center',
  },
  timeTakenColumn: {
    flexShrink: 2,
    width: 150,
    alignItems: 'center',
  },
  correctSubmissionColumn: {
    flexShrink: 1,
    width: 200,
    alignItems: 'center',
  },
  containerWithInfo: {
    flex: 1,
    gap: 10,
    flexDirection: 'column',
    width: '100%',
    maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
  },
  separator: {
    width: '100%',
    height: 1,
    backgroundColor: Dark.colors.tertiary,
    marginVertical: 4,
  },

  // empty leaderboard
  emptyLeaderboardContainer: {
    alignItems: 'center',
    padding: 24,
    gap: 16,
  },
  emptyLeaderboardLabel: {
    color: Dark.colors.textDark,
    fontSize: 14,
    textAlign: 'center',
  },
  playNowButton: {
    width: 100,
    borderRadius: 16,
    backgroundColor: Dark.colors.tertiary,
  },
  playNowLabel: {
    fontSize: 12,
    color: 'white',
  },
  usernameContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
    gap: 6,
  },
  usernameContainerCompact: {
    flexDirection: 'column',
    gap: 2,
  },
  rowLabelCompact: {
    fontSize: 13,
  },
  ratingCompact: {
    fontSize: 12,
  },
});

export default styles;
