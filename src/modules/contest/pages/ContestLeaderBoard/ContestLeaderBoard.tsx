import React from 'react';
import { Text, View } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useContestDetails from '../../hooks/useContestDetails';
import styles from './ContestLeaderBoard.style';
import ContestDetailInfo from './components/ContestDetailInfo';
import ContestantsRankInfo from './ContestantsRankInfo';
import CompactLeaderboardPage from '../../components/CompactLeaderboard';

interface ContestLeaderBoardProps {
  contestId: string;
}

interface ExpandedContestLeaderBoardProps {
  contestId: string;
  contestDetails: any;
}

const ExpandedContestLeaderBoard = React.memo(
  ({ contestId, contestDetails }: ExpandedContestLeaderBoardProps) => (
    <View style={styles.containerWithInfo}>
      <ContestDetailInfo contestId={contestId} />
      <Text style={styles.mainText}>LeaderBoard</Text>
      <ContestantsRankInfo contest={contestDetails} />
    </View>
  ),
);

const ContestLeaderboardContainer = React.memo(
  ({ contestId }: ContestLeaderBoardProps) => {
    const { isMobile: isCompactMode } = useMediaQuery();
    const { loading, error, refetch, contestDetails } = useContestDetails({
      contestId,
    });

    if (loading) {
      return <Loading label="Fetching Contest..." />;
    }

    if (_isEmpty(contestDetails) || error) {
      return (
        <ErrorView
          errorMessage="Something went wrong while fetching Contest"
          onRetry={refetch}
        />
      );
    }

    if (isCompactMode) {
      return <CompactLeaderboardPage contest={contestDetails} />;
    }

    return (
      <ExpandedContestLeaderBoard
        contestDetails={contestDetails}
        contestId={contestId}
      />
    );
  },
);

export default React.memo(ContestLeaderboardContainer);
