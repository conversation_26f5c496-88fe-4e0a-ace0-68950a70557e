import React, { useEffect, useRef, useState } from 'react';
import _isEmpty from 'lodash/isEmpty';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Loading from 'atoms/Loading';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import ErrorView from 'atoms/ErrorView';
import ExpandedContestWaitingPage from 'modules/contest/pages/ContestWaitingPage/Expanded/ExpandedContestWaitingPage';
import CompactContestWaitingPage from 'modules/contest/pages/ContestWaitingPage/Compact/CompactContestWaitingPage';
import LiveContest from '../LiveContest';
import useContestDetails from '../../hooks/useContestDetails';

// TODO: @mohan use the https://worldtimeapi.org/api/timezone/Etc/UTC endpoint to get the global time and set the difference in the local storage.

interface ContestWaitingPageContainerProps {
  contestId: string;
}

const ContestWaitingPageContainer = ({
  contestId,
}: ContestWaitingPageContainerProps) => {
  const {
    contestDetails = EMPTY_OBJECT,
    loading,
    error,
    refetch,
  } = useContestDetails({ contestId });

  const { startTime } = contestDetails;

  const { isMobile } = useMediaQuery();

  const currentTime = getCurrentTimeWithOffset();
  const startTimeDate = new Date(startTime ?? currentTime);
  const timeDiff = Math.max(0, startTimeDate.getTime() - currentTime);

  const [timeLeft, setTimeLeft] = useState(timeDiff);

  const prevTimeoutRef = useRef(null);

  useEffect(() => {
    if (prevTimeoutRef.current) {
      clearTimeout(prevTimeoutRef.current);
    }
    prevTimeoutRef.current = setInterval(() => {
      const newTime = getCurrentTimeWithOffset();
      const timeDiff = startTimeDate.getTime() - newTime;
      setTimeLeft(timeDiff);
    }, 1000);

    return () => clearTimeout(prevTimeoutRef.current);
  }, [startTime]);

  if (loading || _isEmpty(contestDetails)) {
    return <Loading label="Fetching Contest" />;
  }

  if (error) {
    return (
      <ErrorView
        errorMessage="Something went wrong while fetching Contest"
        onRetry={refetch}
      />
    );
  }

  if (timeLeft <= 0) {
    return <LiveContest contest={contestDetails} />;
  }

  const ComponentToBeRendered = isMobile
    ? CompactContestWaitingPage
    : ExpandedContestWaitingPage;

  return (
    <ComponentToBeRendered
      timeLeft={timeLeft}
      contestDetails={contestDetails}
    />
  );
};

ContestWaitingPageContainer.propTypes = {};

export default React.memo(ContestWaitingPageContainer);
