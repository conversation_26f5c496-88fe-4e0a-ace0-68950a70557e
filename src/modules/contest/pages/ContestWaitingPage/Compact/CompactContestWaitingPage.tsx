import React, { useCallback, useEffect } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Text } from '@rneui/themed';
import { getFormattedTime } from 'core/utils/general';
import Header from 'shared/Header';
import useGoBack from 'navigator/hooks/useGoBack';
import LinearGradient from 'atoms/LinearGradient';
import dark from 'core/constants/themes/dark';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import styles from './CompactContestWaitingPage.style';

interface CompactContestWaitingPageProps {
  timeLeft: number;
  contestDetails: any;
}

const CompactContestWaitingPage = ({
  timeLeft,
  contestDetails,
}: CompactContestWaitingPageProps) => {
  const { goBack } = useGoBack();

  const onPressExitWaitingRoom = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.CONTEST.CLICKED_ON_EXIT_WAITING_ROOM);
    goBack();
  }, [goBack]);

  useEffect(() => {
    Analytics.track(ANALYTICS_EVENTS.CONTEST.VISITED_CONTEST_WAITING_PAGE);
  }, []);

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={dark.colors.contestMainPageGradient}
        style={styles.gradient}
      >
        <Header title="Waiting Room " isTransparentBg />
        <View style={styles.contestDetails}>
          <LinearGradient
            colors={dark.colors.contestLogoBgGradient}
            style={styles.gradientBox}
          >
            <View style={styles.iconContainer}>
              <Text style={{ fontSize: 30 }}>🏆</Text>
            </View>
          </LinearGradient>
        </View>
      </LinearGradient>
      <View style={styles.contestTimeAndDesc}>
        <View style={styles.detailsRow}>
          <Text style={styles.contestTitle}>{contestDetails?.name}</Text>
        </View>
        <Text style={styles.hostedBy}>{contestDetails?.description}</Text>
        {contestDetails?.startTime && (
          <Text style={styles.detailsText}>
            {new Date(contestDetails.startTime).toLocaleDateString('en-GB', {
              day: 'numeric',
              month: 'short',
            })}{' '}
            |{' '}
            {new Date(contestDetails.startTime).toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: 'numeric',
            })}{' '}
            -{' '}
            {new Date(contestDetails.endTime).toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: 'numeric',
            })}{' '}
            | {Math.round(contestDetails?.contestDuration / 60)} Min
          </Text>
        )}
      </View>
      <View style={styles.timerContentContainer}>
        <Text style={styles.startingIn}>Contest Starts in</Text>
        <View style={{ width: 45 }}>
          <Text style={styles.leaveChallengeLabel}>
            {getFormattedTime(timeLeft / 1000)}
          </Text>
        </View>
      </View>
      <TouchableOpacity
        onPress={onPressExitWaitingRoom}
        style={[
          styles.fab,
          { backgroundColor: dark.colors.primary, width: '90%' },
        ]}
      >
        <Text style={styles.fabText}>Exit Waiting Room </Text>
      </TouchableOpacity>
    </View>
  );
};

export default React.memo(CompactContestWaitingPage);
