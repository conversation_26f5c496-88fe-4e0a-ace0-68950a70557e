import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    flex: 1,
    width: '100%',
    height: '100%',
    backgroundColor: dark.colors.primary,
  },
  contentContainer: {
    flex: 3,
    gap: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  image: {
    width: 200,
    height: 200,
    maxHeight: 200,
  },
  compactImage: {
    width: 120,
    height: 120,
  },
  textContainer: {
    gap: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontFamily: 'Montserrat-600',
    color: 'white',
  },
  description: {
    fontSize: 14,
    fontFamily: 'Montserrat-600',
    justifyContent: 'center',
    textAlign: 'center',
    color: dark.colors.textDark,
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 36,
  },
  buttonContainerWeb: { flex: 1 },
  buttonStyle: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 8,
  },
  contestDetails: {
    marginLeft: 10,
    marginRight: 10,
    width: '100%',
    overflow: 'hidden',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
    gap: 10,
  },
  contestTimeAndDesc: {
    borderRadius: 8,
    display: 'flex',
    alignItems: 'center',
    marginHorizontal: 13,
    marginVertical: 20,
    gap: 5,
  },
  contestImage: {
    width: 50,
    height: 50,
    marginBottom: 10,
  },
  contestTitle: {
    color: dark.colors.textLight,
    fontSize: 16,
    textAlign: 'center',
    fontFamily: 'Montserrat-700',
    flexWrap: 'wrap',
    display: 'flex',
    marginHorizontal: 10,
  },
  hostedBy: {
    color: dark.colors.textDark,
    fontSize: 12,
    textAlign: 'center',
    fontFamily: 'Montserrat-700',
    flexWrap: 'wrap',
    display: 'flex',
    marginHorizontal: 10,
  },
  gradientBox: {
    height: 70,
    width: 70,
    borderRadius: 10,
    marginTop: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  detailsText: {
    color: dark.colors.textLight,
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    marginBottom: 5,
  },
  instructionContainer: {
    width: '80%',
    gap: 12,
    marginTop: 24,
  },
  instructionTitle: {
    fontSize: 16,
    fontFamily: 'Montserrat-700',
    alignItems: 'center',
    textAlign: 'center',
    color: dark.colors.textDark,
  },
  instructionDescriptionContainer: {
    alignItems: 'center',
    textAlign: 'center',
    flexDirection: 'row',
    gap: 12,
  },
  instructionDescription: {
    fontSize: 14,
    fontFamily: 'Montserrat-400',
    color: dark.colors.textLight,
  },
  bullet: {
    backgroundColor: dark.colors.secondary,
    height: 4,
    width: 4,
    minHeight: 4,
    minWidth: 4,
    borderRadius: 4,
    alignSelf: 'center',
    shadowColor: dark.colors.secondary,
    shadowOpacity: 1.0,
    shadowOffset: { width: 0, height: 0 },
    shadowRadius: 8,
    elevation: 20,
    web: {
      shadowRadius: 20,
    },
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: dark.colors.secondary,
    padding: 15,
    borderRadius: 50,
    alignItems: 'center',
    elevation: 8,
  },
  fabText: {
    fontSize: 16,
    fontFamily: 'Montserrat-600',
    color: dark.colors.secondary,
  },
  timerContainer: {
    gap: 36,
  },
  timerContentContainer: {
    flexDirection: 'row',
    marginTop: 20,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  startingIn: {
    fontSize: 20,
    fontFamily: 'Montserrat-600',
    color: 'white',
  },
  leaveChallengeLabel: {
    fontSize: 16,
    textAlign: 'center',
    fontFamily: 'Montserrat-700',
    color: '#FA9191',
  },
});

export default styles;
