import React, { useCallback, useEffect } from 'react';

import { TouchableOpacity, View } from 'react-native';

import { Text } from '@rneui/themed';
import LinearGradient from 'atoms/LinearGradient';
import dark from 'core/constants/themes/dark';
import useGoBack from 'navigator/hooks/useGoBack';
import { getFormattedTime } from 'core/utils/general';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';

import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import styles from './ExpandedContestWaitingPage.style';
import { getContestPropertiesToTrack } from '../../../utils/contestEvents';

interface ExpandedContestWaitingPageProps {
  timeLeft: number;
  contestDetails: any;
}

const ExpandedContestWaitingPage = React.memo(
  ({ timeLeft = 0, contestDetails }: ExpandedContestWaitingPageProps) => {
    const { goBack } = useGoBack();

    const renderInfoRowContent = useCallback(
      () => (
        <View style={styles.infoRow}>
          <View style={styles.infoBox}>
            <Text style={styles.infoTitle}>Contest open time</Text>
            <Text style={styles.infoDetails}>
              {new Date(contestDetails?.startTime).toLocaleString()}
            </Text>
          </View>
          <View style={styles.infoBox}>
            <Text style={styles.infoTitle}>Contest close time</Text>
            <Text style={styles.infoDetails}>
              {new Date(contestDetails?.endTime).toLocaleString()}
            </Text>
          </View>
          <View style={styles.infoBox}>
            <Text style={styles.infoTitle}>Duration</Text>
            <Text style={styles.infoDetails}>
              {Math.round(contestDetails?.contestDuration / 60)} Minutes
            </Text>
          </View>
        </View>
      ),
      [contestDetails],
    );

    const renderHeaderContent = useCallback(
      () => (
        <View style={styles.header}>
          <View>
            <LinearGradient
              colors={dark.colors.contestLogoBgGradient}
              style={styles.gradientBox}
            >
              <View style={styles.iconContainer}>
                <Text style={styles.title}>🏆</Text>
              </View>
            </LinearGradient>
          </View>
          <View>
            <View style={styles.liveDetail}>
              <Text style={styles.title}>{contestDetails?.name}</Text>
            </View>
            <Text style={styles.hostName}>
              {`Hosted By ${contestDetails?.hostedBy}`}
            </Text>
          </View>
        </View>
      ),
      [contestDetails],
    );

    const onPressExitWaitingRoom = useCallback(() => {
      Analytics.track(ANALYTICS_EVENTS.CONTEST.CLICKED_ON_EXIT_WAITING_ROOM, {
        ...getContestPropertiesToTrack({ contest: contestDetails }),
        [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_WAITING_ROOM,
      });
      goBack();
    }, [goBack, contestDetails]);

    useEffect(() => {
      Analytics.track(ANALYTICS_EVENTS.CONTEST.VISITED_CONTEST_WAITING_PAGE, {
        ...getContestPropertiesToTrack({ contest: contestDetails }),
        [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_WAITING_ROOM,
      });
    }, []);

    return (
      <View style={styles.container}>
        <View
          style={{
            justifyContent: 'center',
            width: '50%',
            paddingTop: 23,
            paddingLeft: 36,
            paddingRight: 36,
            alignItems: 'center',
            borderColor: dark.colors.tertiary,
            borderWidth: 2,
            borderRadius: 15,
          }}
        >
          {renderHeaderContent()}
          {renderInfoRowContent()}
          <View style={styles.timerContentContainer}>
            <Text style={styles.startingIn}>Contest Starts in</Text>
            <View style={{ width: 100 }}>
              <Text style={styles.leaveChallengeLabel}>
                {getFormattedTime(timeLeft / 1000)}
              </Text>
            </View>
          </View>
          <TouchableOpacity
            onPress={onPressExitWaitingRoom}
            style={{
              width: '100%',
              alignItems: 'center',
              marginTop: 32,
              marginBottom: 23,
            }}
          >
            <Text style={styles.fabText}>Exit Waiting Room </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  },
);

export default React.memo(ExpandedContestWaitingPage);
