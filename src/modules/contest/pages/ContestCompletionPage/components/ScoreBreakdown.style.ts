import { StyleSheet } from 'react-native';
import Dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
    container: {
        flex: 1,
        maxHeight:'auto',
        backgroundColor: Dark.colors.background,
        marginBottom:20
    },
    headerContainer: {
        flexDirection: 'row',
        paddingVertical: 12,
        paddingHorizontal: 16,
        backgroundColor: Dark.colors.primary,
        borderTopLeftRadius: 8,
        borderTopRightRadius: 8,
    },
    headerLabel: {
        fontSize: 14,
        fontFamily: 'Montserrat-700',
        color: Dark.colors.text,
    },
    rowContainer: {
        flexDirection: 'row',
        paddingVertical: 12,
        paddingHorizontal: 16,
    },
    rowLabel: {
        fontSize: 14,
        color: Dark.colors.text,
    },
    questionColumn: {
        flex: 3,
        paddingRight:8
    },
    yourScoreColumn: {
        flex: 2,
        alignItems: 'center',
    },
    submissionTimeColumn: {
        flex: 3,
        alignItems: 'center',
    },
    maxScoreColumn: {
        flex: 1,
        alignItems: 'center',
    },
    scoredColumn: {
        flex: 1,
        alignItems: 'center',
    },
    separator: {
        height: 1,
        backgroundColor: Dark.colors.border,
    },
    viewMoreButtonContainer: {
        marginVertical: 16,
        marginHorizontal: 16,
    },
    viewMoreButton: {
        backgroundColor: Dark.colors.primary,
        borderRadius: 8,
    },
    viewMoreButtonTitle: {
        fontSize: 14,
        fontFamily: 'Montserrat-700',
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyText: {
        fontSize: 16,
        color: Dark.colors.text,
    },
});

export default styles;
