import React, { useCallback, useMemo } from 'react';
import { FlatList, Text, View } from 'react-native';
import styles from './ScoreBreakdown.style';
import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';

const HEADER_ITEMS = [
  {
    label: 'Question',
    style: styles.questionColumn,
  },
  {
    label: 'Max Score',
    style: styles.maxScoreColumn,
  },
  {
    label: 'Achieved Score',
    style: styles.yourScoreColumn,
  },
  {
    label: 'Submission Time',
    style: styles.submissionTimeColumn,
  },
];

const ScoreBreakdown = ({
  questionList,
  scoreList,
}: {
  questionList: any[];
  scoreList: any;
}) => {
  const combinedList = useMemo(() => {
    return questionList.map((question) => {
      const submission = _find(scoreList.submissions, {
        questionId: question.question.id,
      });
      return {
        ...question,
        submission,
      };
    });
  }, [questionList, scoreList]);

  const renderHeader = useCallback(
    () => (
      <View style={styles.headerContainer}>
        {_map(HEADER_ITEMS, (item, index) => (
          <View key={index} style={item.style}>
            <Text
              style={styles.headerLabel}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {item.label}
            </Text>
          </View>
        ))}
      </View>
    ),
    [],
  );

  const renderRow = useCallback(
    ({ item }: { item: any }) => (
      <View style={styles.rowContainer}>
        <View style={styles.questionColumn}>
          <Text style={styles.rowLabel} numberOfLines={1} ellipsizeMode="tail">
            {item.question.expression.join(' ')}
          </Text>
        </View>
        <View style={styles.maxScoreColumn}>
          <Text style={styles.rowLabel} numberOfLines={1} ellipsizeMode="tail">
            {item.points}
          </Text>
        </View>
        <View style={styles.yourScoreColumn}>
          <Text style={styles.rowLabel} numberOfLines={1} ellipsizeMode="tail">
            {item.submission ? item.submission.points : 0}
          </Text>
        </View>
        <View style={styles.submissionTimeColumn}>
          <Text style={styles.rowLabel} numberOfLines={1} ellipsizeMode="tail">
            {item.submission
              ? new Date(item.submission.submissionTime).toLocaleString()
              : 'Not Submitted'}
          </Text>
        </View>
      </View>
    ),
    [],
  );

  const renderSeparator = useCallback(
    () => <View style={styles.separator} />,
    [],
  );

  if (_isEmpty(combinedList)) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No score breakdown available</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {renderHeader()}
      <FlatList
        data={combinedList}
        renderItem={renderRow}
        keyExtractor={(item) => item.question.id}
        ItemSeparatorComponent={renderSeparator}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default React.memo(ScoreBreakdown);
