import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { Platform, Text, View } from 'react-native';
import { format } from 'date-fns';
import ErrorView from 'atoms/ErrorView';
import Loading from 'atoms/Loading';
import Header from 'shared/Header';
import LinearGradientText from 'atoms/LinearGradientText';
import dark from 'core/constants/themes/dark';
import { getFormattedTimeWithMS } from 'core/utils/general';
import SecondaryButton from 'atoms/SecondaryButton';
import { useRouter } from 'expo-router';
import useMediaQuery from 'core/hooks/useMediaQuery';
import contestBadge from 'assets/images/contest_badge.png';
import CardWithCTA from 'shared/CardWithCTA/CardWithCTA';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useGoBack from 'navigator/hooks/useGoBack';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import AppDownloadBanner from 'shared/AppDownloadModal/AppDownloadBanner';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import contestReader from 'core/readers/contestReader';
import { getContestPropertiesToTrack } from '../../utils/contestEvents';
import { getTimeSpentByUser } from '../../utils/contest';
import useGetUserSubmissionInContest from '../../hooks/queries/useGetUserSubmissionInContest';
import useContestCompletionStyles from './ContestCompletionPage.style';

const ContestCompletionPage = ({ contest }: { contest: any }) => {
  const { isMobile: isCompactMode } = useMediaQuery();

  const styles = useContestCompletionStyles();
  const router = useRouter();
  const { error, loading, refetch, userSubmission } =
    useGetUserSubmissionInContest({ contestId: contest?._id });

  const { goBack } = useGoBack();

  const handleOnGoBackPressed = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.CONTEST.CLICKED_ON_CONTEST_COMPLETION_BACK,
      {
        [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_COMPLETION_PAGE,
      },
    );
    goBack();
  }, [goBack]);

  const refetchRef = useRef(refetch);
  refetchRef.current = refetch;

  const contestId = contestReader.id(contest);
  const endTime = contestReader.endTime(contest);
  const currentUserParticipation =
    contestReader.currentUserParticipation(contest);

  const { submissions, totalScore } = userSubmission ?? EMPTY_OBJECT;

  const totalNumberOfSubmissions = _size(submissions);

  const endDateTime = new Date(endTime);

  const timeTaken = getTimeSpentByUser({
    contest,
    participantSubmission: userSubmission,
  });
  const formattedTimeTaken = getFormattedTimeWithMS(timeTaken);

  const contestEndedForAll =
    endDateTime.getTime() <= getCurrentTimeWithOffset();

  const renderContestStat = useCallback(
    ({ label, value }: { label: string; value: number }) => (
      <View style={styles.contestStatContainer} key={label}>
        <Text style={styles.statValue}>{value}</Text>
        <Text style={styles.statLabel}>{label}</Text>
      </View>
    ),
    [styles.contestStatContainer, styles.statLabel, styles.statValue],
  );

  const stats = [
    {
      label: 'Total Score',
      value: totalScore,
    },
    {
      label: 'Total Time (mm:ss)',
      value: formattedTimeTaken,
    },
  ];

  const onPressLeaderBoard = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.CONTEST.CLICKED_ON_CONTEST_COMPLETED_PAGE_LEADERBOARD,
      {
        ...getContestPropertiesToTrack({ contest }),
        [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_COMPLETION_PAGE,
      },
    );
    router.push(`/contest/leaderboard/${contestId}`);
  }, [router, contest, contestId]);

  const onPressGoHome = useCallback(() => router.push(`/home`), [router]);

  const contestRef = useRef(contest);
  contestRef.current = contest;

  const trackingProperties = useMemo(
    () => ({
      ...getContestPropertiesToTrack({ contest }),
      [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_COMPLETION_PAGE,
    }),
    [contest],
  );

  useEffect(() => {
    Analytics.track(ANALYTICS_EVENTS.CONTEST.VISITED_CONTEST_COMPLETION_PAGE, {
      ...getContestPropertiesToTrack({ contest: contestRef.current }),
    });
    setTimeout(() => refetchRef.current(), 500);
  }, []);

  if (_isEmpty(currentUserParticipation)) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorMessage}>
          You have not registered for this contest
        </Text>
        <SecondaryButton
          onPress={onPressGoHome}
          label="Go To Home"
          radius={8}
          buttonStyle={{ width: 200 }}
        />
      </View>
    );
  }

  if (error) {
    return (
      <ErrorView errorMessage="Something went wrong while loading your submission. Please try again later." />
    );
  }

  if (_isEmpty(userSubmission) && loading) {
    return <Loading label="Loading your submissions" />;
  }

  return (
    <View style={styles.container}>
      <Header title="" goBack={handleOnGoBackPressed} isTransparentBg />
      <View style={{ marginHorizontal: isCompactMode ? 16 : 0 }}>
        {totalNumberOfSubmissions === 0 ? (
          <LinearGradientText
            textStyle={styles.congratsOrNotAttemptedText}
            colors={[dark.colors.textLight, dark.colors.textDark]}
          >
            {Platform.OS !== 'web' ? (
              <Text style={styles.congratsOrNotAttemptedText}>
                You have not submitted any answers in this contest!
              </Text>
            ) : (
              'You have not submitted any answers in this contest!'
            )}
          </LinearGradientText>
        ) : (
          <LinearGradientText
            textStyle={styles.congratsOrNotAttemptedText}
            colors={[dark.colors.textLight, dark.colors.textDark]}
          >
            {Platform.OS !== 'web' ? (
              <Text style={styles.congratsOrNotAttemptedText}>
                Congratulations on completing the contest!
              </Text>
            ) : (
              'Congratulations on completing the contest!'
            )}
          </LinearGradientText>
        )}
      </View>
      <View style={styles.statsContainer}>{stats.map(renderContestStat)}</View>
      {contestEndedForAll && (
        <View style={{ marginHorizontal: isCompactMode ? 16 : 0 }}>
          <SecondaryButton
            onPress={onPressLeaderBoard}
            label="View LeaderBoard"
            radius={8}
            buttonStyle={{ width: 200 }}
          />
        </View>
      )}
      {!contestEndedForAll && (
        <View
          style={{
            marginHorizontal: isCompactMode ? 16 : 0,
            marginVertical: isCompactMode ? 10 : 0,
          }}
        >
          <CardWithCTA
            buttonText="Live Leaderboard"
            imageSource={contestBadge}
            onButtonPress={onPressLeaderBoard}
            titleText={`Question Level Analysis will be released on ${format(endDateTime, 'MMMM d, yyyy, h:mm:ss aa zzz')}`}
          />
        </View>
      )}

      <View
        style={{
          marginHorizontal: isCompactMode ? 16 : 0,
          marginVertical: isCompactMode ? 10 : 0,
        }}
      >
        <AppDownloadBanner trackingProperties={trackingProperties} />
      </View>
    </View>
  );
};

export default ContestCompletionPage;
