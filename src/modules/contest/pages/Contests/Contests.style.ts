import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { useMemo } from 'react';
import { StyleSheet } from 'react-native';

const createStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    featuredContests: {
      width: isCompactMode ? '90%' : '80%',
      justifyContent: 'center',
      marginLeft: isCompactMode ? 8 : 0,
    },
    headingText: {
      fontSize: 20,
      fontFamily: 'Montserrat-500',
      textAlign: 'left',
      color: '#FFFFFF',
      marginHorizontal: 20,
      marginVertical: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    contestMain: {
      paddingHorizontal: !isCompactMode ? 36 : 0,
      paddingTop: !isCompactMode ? 28 : 0,
      flex: 1,
    },
  });

const useContestsPageStyles = () => {
  const { isMobile: isCompactMode } = useMediaQuery();
  return useMemo(() => createStyles(isCompactMode), [isCompactMode]);
};

export default useContestsPageStyles;
