import React from 'react'
import { View, Text } from 'react-native'
import useMediaQuery from 'core/hooks/useMediaQuery'
import ContestTabBar from '../../components/ContestListTabBar/ContestsTabBar'
import useContestsPageStyles from './Contests.style'

const Contests = () => {
  const { isMobile: isCompactMode } = useMediaQuery()
  const styles = useContestsPageStyles()

  return (
    <View style={styles.contestMain}>
      {isCompactMode && <Text style={styles.headingText}>Contests</Text>}
      <ContestTabBar />
    </View>
  )
}

export default React.memo(Contests)
