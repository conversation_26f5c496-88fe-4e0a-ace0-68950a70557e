import { View } from 'react-native';
import JoinNowOrRegisterButtonsAndInfos from '../../../components/JoinNowOrRegisterButtonsAndInfo';

interface ContestDetailsRightPaneProps {
  contestDetails: any;
  loading: boolean;
  error: any;
  refetch: Function;
}

const ContestDetailsRightPane = ({
  contestDetails,
  loading,
  error,
  refetch,
}: ContestDetailsRightPaneProps) => (
  <View style={{ width: '25%', justifyContent: 'flex-start' }}>
    <JoinNowOrRegisterButtonsAndInfos
      contestDetails={contestDetails}
      loading={loading}
      error={error}
      refetch={refetch}
    />
  </View>
);

export default ContestDetailsRightPane;
