import React from 'react';

import useMediaQuery from 'core/hooks/useMediaQuery';
import CompactContestDetail from 'modules/contest/components/CompactContestDetail';
import useContestDetails from '../../hooks/useContestDetails';

import ExpandedContestDetails from './ExpandedContestDetails';

const ContestDetailsContainer = ({ contestId }: { contestId: string }) => {
  const { isMobile: isCompactMode } = useMediaQuery();

  const contestDetailsData = useContestDetails({
    contestId,
  });

  const ContestDetailsComponent = isCompactMode
    ? CompactContestDetail
    : ExpandedContestDetails;

  return <ContestDetailsComponent {...contestDetailsData} />;
};

export default React.memo(ContestDetailsContainer);
