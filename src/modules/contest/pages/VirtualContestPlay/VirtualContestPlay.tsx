import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { KeyboardAvoidingView, Platform, View } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';

import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import useMediaQuery from 'core/hooks/useMediaQuery';
import WithCountdown from 'shared/WithCountdown';

import styles from './VirtualContestPlay.style';
import useContestDetails from '../../hooks/useContestDetails';
import ContestCompletionPage from '../ContestCompletionPage';
import Question from '../../../game/pages/PlayGame/Question';
import Footer from '../../../game/pages/PlayGame/Footer';
import useContestQuestionState from '../../hooks/useContestQuestionState';
import useGetUserSubmissionInContest from '../../hooks/queries/useGetUserSubmissionInContest';
import useUpdateContestParticipantStartTime from '../../hooks/mutations/useUpdateContestParticipantStartTime';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import Header from '../LiveContest/components/Header';

interface VirtualContestPlayProps {
  contest: any;
  userSubmission: any;
}

export const CountdownKeyboardView = WithCountdown(
  ({ children }) => (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 48 : 36}
    >
      {children}
    </KeyboardAvoidingView>
  ),
  { timer: 3 },
);

const VirtualContestPlay = ({
  contest,
  userSubmission,
}: VirtualContestPlayProps) => {
  const { isMobile } = useMediaQuery();
  const { updateContestParticipantStartTime } =
    useUpdateContestParticipantStartTime();

  const { startTime, _id: contestId, contestDuration = 0 } = contest;

  const startDateTime = new Date(startTime);

  const { startTime: userStartTime } = userSubmission ?? EMPTY_OBJECT;
  const startTimeForUser = useMemo(
    () => userStartTime ?? getCurrentTimeWithOffset(),
    [userStartTime],
  );

  const endTimeForUser =
    new Date(startTimeForUser).getTime() + contestDuration * 1000;

  const hasContestEndedForUser = endTimeForUser <= getCurrentTimeWithOffset();
  const isContestLiveForUser =
    startDateTime <= getCurrentTimeWithOffset() &&
    endTimeForUser >= getCurrentTimeWithOffset();

  const {
    currentQuestion,
    submitAnswer,
    currentScore,
    solvedAllQuestions,
    submittedAllAnswersSuccessFully,
  } = useContestQuestionState({
    contest,
    initialUserSubmission: userSubmission,
    isVirtualContest: true,
  });

  const renderFooter = useCallback(
    () => (
      <View style={[styles.footerContainer]}>
        <Footer question={currentQuestion} submitAnswer={submitAnswer} />
      </View>
    ),
    [currentQuestion],
  );

  useEffect(() => {
    if (_isNil(userStartTime)) {
      try {
        updateContestParticipantStartTime({
          contestId,
          startTime: startTimeForUser,
        });
      } catch (error) {
        // console.log(error);
      }
    }
  }, [contestId]);

  const [shouldForceUpdate, setShouldForceUpdate] = useState(false);

  useEffect(() => {
    const checkTimeRemaining = () => {
      const currentTime = getCurrentTimeWithOffset();
      if (currentTime >= endTimeForUser && !shouldForceUpdate) {
        setShouldForceUpdate(true);
      }
    };
    const interval = setInterval(checkTimeRemaining, 1000);
    return () => clearInterval(interval);
  }, [endTimeForUser, shouldForceUpdate]);

  const renderCompletionPage = useCallback(
    () => (
      <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
        <ContestCompletionPage contest={contest} currentScore={currentScore} />
      </View>
    ),
    [contest, currentScore],
  );

  if (hasContestEndedForUser || shouldForceUpdate) {
    return renderCompletionPage();
  }

  if (solvedAllQuestions) {
    if (submittedAllAnswersSuccessFully) {
      return renderCompletionPage();
    }
    return <Loading />;
  }

  if (!isContestLiveForUser) {
    return null;
  }

  return (
    <CountdownKeyboardView>
      <View
        style={[styles.mainContainer, !isMobile && styles.mainContainerWeb]}
      >
        <View style={[styles.container, !isMobile && styles.webContainer]}>
          <View style={isMobile && styles.mobileHeader}>
            <Header
              contest={contest}
              currentScore={currentScore}
              startTimeForUser={startTimeForUser}
              isVirtualContest
            />
          </View>
          <View style={[styles.question]}>
            <Question question={currentQuestion} />
          </View>
          {!isMobile && renderFooter()}
        </View>
        {isMobile && renderFooter()}
      </View>
    </CountdownKeyboardView>
  );
};

const VirtualContestPlayContainer = (props) => {
  const { contestId } = props;

  const {
    contestDetails = EMPTY_OBJECT,
    loading: contestLoading,
    error,
    refetch,
  } = useContestDetails({ contestId });

  const { userSubmission, loading: submissionLoading } =
    useGetUserSubmissionInContest({
      contestId,
    });

  if (_isEmpty(contestDetails) || contestLoading || submissionLoading) {
    return <Loading />;
  }

  if (!_isEmpty(error)) {
    return (
      <ErrorView
        errorMessage="Something went wrong while fetching Contest Questions"
        onRetry={refetch}
      />
    );
  }

  const { currentUserParticipation } = contestDetails;

  if (_isEmpty(currentUserParticipation)) {
    return (
      <ErrorView errorMessage="You have not registered for this contest" />
    );
  }

  return (
    <VirtualContestPlay
      userSubmission={userSubmission}
      contest={contestDetails}
    />
  );
};

export default React.memo(VirtualContestPlayContainer);
