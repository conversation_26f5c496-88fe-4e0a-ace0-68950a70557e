import { UserResultType } from '../types/puzzleType';
import _get from 'lodash/get';

const puzzleResultReader = {
  id: (puzzleResult: UserResultType): string => _get(puzzleResult, 'id'),
  userId: (puzzleResult: UserResultType): string =>
    _get(puzzleResult, 'userId'),
  puzzleId: (puzzleResult: UserResultType): string =>
    _get(puzzleResult, 'puzzleId'),
  timeSpent: (puzzleResult: UserResultType | null): number | undefined =>
    _get(puzzleResult, 'timeSpent', 0),
  completedAt: (puzzleResult: UserResultType): string =>
    _get(puzzleResult, 'completedAt'),
  puzzleDate: (puzzleResult: UserResultType): string =>
    _get(puzzleResult, 'puzzleDate'),
  statikCoinsEarned: (puzzleResult: UserResultType): number =>
    _get(puzzleResult, 'statikCoinsEarned', 0),
};

export default puzzleResultReader;
