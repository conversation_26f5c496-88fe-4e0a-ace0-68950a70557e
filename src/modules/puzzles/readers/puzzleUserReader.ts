import { UserStatType } from '../types/puzzleType';
import _get from 'lodash/get';

const puzzleStatsReader = {
  id: (stats: UserStatType): string => _get(stats, 'id'),
  userId: (stats: UserStatType): string => _get(stats, 'userId'),
  numOfSubmission: (stats: UserStatType): number | undefined =>
    _get(stats, 'numOfSubmission', 0),
  averageTime: (stats: UserStatType | null): number | undefined =>
    _get(stats, 'averageTime', 0),
  bestTime: (stats: UserStatType | null): number | undefined =>
    _get(stats, 'bestTime', 0),
};

export default puzzleStatsReader;
