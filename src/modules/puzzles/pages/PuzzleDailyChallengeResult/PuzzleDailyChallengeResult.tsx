import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import CrossMathPuzzleResultHeader from 'modules/puzzles/components/crossMathPuzzleResult/CrossMathPuzzleResultHeader';
import puzzleReader from 'modules/puzzles/readers/puzzleReader';
import _isNil from 'lodash/isNil';
import ErrorView from 'atoms/ErrorView';
import { router, useLocalSearchParams } from 'expo-router';
import Loading from 'atoms/Loading';
import {
  PUZZLE_TYPES,
  PuzzleType,
  VALID_PUZZLE_TYPES,
} from 'modules/puzzles/types/puzzleType';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Rive from 'atoms/Rive';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import useSound from 'core/hooks/useSound';
import tada_drumroll_sound from 'assets/audio/tada_drumroll.wav';
import _includes from 'lodash/includes';
import CrossMathPuzzleResultFooter from '../../components/crossMathPuzzleResult/CrossMathPuzzleResultFooter';
import CrossMathPuzzleResultInfoCard from '../../components/crossMathPuzzleResult/CrossMathPuzzleResultInfoCard';
import usePuzzleDailyChallengeResultPageController from '../../hooks/usePuzzleDailyChallengeResultPageController';

const PuzzleDailyChallengeResult = ({
  date,
  puzzle,
  puzzleType,
}: {
  date: string | string[];
  puzzle: PuzzleType;
  puzzleType: string;
}) => {
  const [showConfettiAnimation, setShowConfettiAnimation] =
    useState<boolean>(true);

  useSound({
    soundFile: tada_drumroll_sound,
    playOnMount: true,
    config: {
      volume: 0.2,
    },
  });

  useEffect(() => {
    Analytics.track(ANALYTICS_EVENTS.CROSS_MATH_PUZZLE.VIEWED_PUZZLE_RESULT, {
      date,
    });
    const timeoutId = setTimeout(() => {
      setShowConfettiAnimation(false);
    }, 2000);
    return () => {
      clearTimeout(timeoutId);
    };
  }, [date]);

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      {showConfettiAnimation && (
        <Rive
          url={RIVE_ANIMATIONS.CONFETTI_ANIMATION}
          autoPlay
          loop={false}
          style={{
            position: 'absolute',
            zIndex: 100,
            top: 0,
            left: 0,
            right: 0,
            width: '100%',
            height: '100%',
          }}
        />
      )}
      <CrossMathPuzzleResultHeader puzzle={puzzle} date={date} />
      <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
        <CrossMathPuzzleResultInfoCard
          puzzle={puzzle}
          puzzleType={puzzleType}
        />
      </View>
      <CrossMathPuzzleResultFooter puzzle={puzzle} date={date} />
    </View>
  );
};

const PuzzleDCResultContainer = ({ date }: { date: string | string[] }) => {
  const {
    puzzleType = PUZZLE_TYPES.CROSS_MATH_PUZZLE,
  }: { puzzleType: string } = useLocalSearchParams();

  const { puzzle, loading, error } =
    usePuzzleDailyChallengeResultPageController({
      puzzleDate: date,
      puzzleType,
    });

  if (!_includes(VALID_PUZZLE_TYPES, puzzleType)) {
    return <ErrorView errorMessage="Invalid puzzle type" />;
  }

  if (loading) {
    return <Loading label="Loading Puzzle Result" />;
  }

  if (error) {
    return <ErrorView errorMessage="Something went wrong" />;
  }

  const userResult = puzzleReader.currentUserResult(puzzle);

  const renderToPuzzlePage = () => {
    router.replace(`/puzzle/daily-challenge/${date}?puzzleType=${puzzleType}`);
  };

  const emptyUserResultActionConfig = {
    label: 'Solve this puzzle',
    onPress: renderToPuzzlePage,
  };

  if (_isNil(userResult)) {
    return (
      <ErrorView
        errorMessage="You have not solved this Puzzle"
        actionConfig={emptyUserResultActionConfig}
      />
    );
  }

  return (
    <PuzzleDailyChallengeResult
      date={date}
      puzzle={puzzle}
      puzzleType={puzzleType}
    />
  );
};

export default React.memo(PuzzleDCResultContainer);
