import { useLocalSearchParams } from 'expo-router';
import React, { useEffect } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import ErrorView from 'atoms/ErrorView';
import { checkIsValidDate } from 'modules/puzzles/utils/puzzleUtils';
import _includes from 'lodash/includes';
import { VALID_PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import { WithErrorBoundary } from 'atoms/ErrorBoundary';
import PuzzleDailyChallengeResult from './PuzzleDailyChallengeResult';

const TRACKED_ATTEMPT_FOR_DATE = {};

const PuzzleDailyChallengeResultContainer = () => {
  const { date } = useLocalSearchParams();

  const { puzzleType }: { puzzleType: string } = useLocalSearchParams();

  const isValidDate = checkIsValidDate(date);

  useEffect(() => {
    if (TRACKED_ATTEMPT_FOR_DATE[date]) return;
    TRACKED_ATTEMPT_FOR_DATE[date] = true;
    Analytics.track(
      ANALYTICS_EVENTS.CROSS_MATH_PUZZLE.VIEWED_CROSS_MATH_RESULT_PAGE,
      {
        date,
        puzzleType,
      },
    );
  }, [date, puzzleType]);

  if (!_includes(VALID_PUZZLE_TYPES, puzzleType)) {
    return (
      <ErrorView errorMessage="Sorry, Puzzle Result is not available for selected date" />
    );
  }

  if (!isValidDate) {
    return (
      <ErrorView errorMessage="Sorry, Puzzle Result is not available for selected date" />
    );
  }

  return <PuzzleDailyChallengeResult date={date} />;
};

PuzzleDailyChallengeResultContainer.displayName =
  'PuzzleDailyChallengeResultContainer';

export default React.memo(
  WithErrorBoundary(PuzzleDailyChallengeResultContainer),
);
