import { ScrollView, View } from 'react-native';
import React, { useCallback, useEffect, useRef } from 'react';
import Loading from 'atoms/Loading';
import _isEmpty from 'lodash/isEmpty';
import usePrevious from 'core/hooks/usePrevious';
import ErrorView from 'atoms/ErrorView';
import _isNil from 'lodash/isNil';
import { router } from 'expo-router';
import ACTIVITY_TYPES from 'core/constants/activityTypes';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useUserActivityTracker from 'core/hooks/useUserActivityTracker';
import useMediaQuery from 'core/hooks/useMediaQuery';
import KenKenPuzzleQuestion from 'shared/KenKenPuzzleQuestion/KenKenPuzzleQuestion';
import KenKenPuzzleHeader from 'modules/puzzles/components/KenKenPuzzleHeader';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import useGetDailyPuzzle from 'modules/puzzles/hooks/queries/useGetDailyPuzzle';
import puzzleReader from 'modules/puzzles/readers/puzzleReader';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import useSubmitPuzzleSolution from '../../hooks/mutations/useSubmitPuzzleSolution';

const KenKenPuzzle = React.memo(({ date }: { date: string | string[] }) => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const { puzzle, loading, error } = useGetDailyPuzzle({
    date,
    puzzleType: PUZZLE_TYPES.KEN_KEN_PUZZLE,
  });

  const { updateActivity } = useUserActivityTracker();
  const { submitPuzzleSolution } = useSubmitPuzzleSolution({
    puzzleId: puzzleReader.id(puzzle),
    puzzleDate: date,
    puzzleType: PUZZLE_TYPES.KEN_KEN_PUZZLE,
  });

  const hasSolved = puzzleReader.hasAttempted(puzzle);

  const puzzleResult = puzzleReader.currentUserResult(puzzle);

  const previousPuzzleResult = usePrevious(puzzleResult);

  const previousPuzzleResultRef = useRef(previousPuzzleResult);
  previousPuzzleResultRef.current = previousPuzzleResult;

  const onSubmitPuzzle = useCallback(
    ({ timeSpent }: { timeSpent: number }) => {
      if (hasSolved) return;
      updateActivity({
        activityType: ACTIVITY_TYPES.DAILY_PUZZLE,
        duration: timeSpent,
      });

      Analytics.track(ANALYTICS_EVENTS.KEN_KEN_PUZZLE.SOLVED_KEN_KEN_PUZZLE, {
        timeSpent,
        date,
      });
      submitPuzzleSolution({ timeSpent })
        .then(() =>
          router.replace(
            `/puzzle/daily-challenge/${date}/result?puzzleType=${PUZZLE_TYPES.KEN_KEN_PUZZLE}`,
          ),
        )
        .catch((e) => {
          Analytics.track(
            ANALYTICS_EVENTS.KEN_KEN_PUZZLE
              .ERROR_WHILE_SUBMITTING_KEN_KEN_PUZZLE,
            { error: e },
          );
          showToast({
            type: TOAST_TYPE.ERROR,
            description: 'Something went wrong while submitting the puzzle',
          });
        });
    },
    [hasSolved, updateActivity, date, submitPuzzleSolution],
  );

  useEffect(() => {
    if (hasSolved && !_isEmpty(puzzleResult)) {
      router.replace(
        `/puzzle/daily-challenge/${date}/result?puzzleType=${PUZZLE_TYPES.KEN_KEN_PUZZLE}`,
      );
    }
  }, [date, hasSolved, puzzleResult]);

  if (loading || _isNil(loading)) {
    return <Loading label="Loading Puzzle..." />;
  }

  if (error) {
    return (
      <ErrorView errorMessage="Something went wrong while loading puzzle" />
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <KenKenPuzzleQuestion
        puzzle={puzzle}
        onSubmitPuzzle={onSubmitPuzzle}
        shouldCacheTime
        shouldCacheGrid
      >
        <KenKenPuzzleHeader showTimer />
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            padding: 8,
            gap: isCompactMode ? 32 : 64,
            justifyContent: isCompactMode ? 'space-around' : 'center',
            alignItems: 'center',
          }}
          showsVerticalScrollIndicator={false}
        >
          <KenKenPuzzleQuestion.Grid />
          <KenKenPuzzleQuestion.Actions />
          <KenKenPuzzleQuestion.Options />
        </ScrollView>
      </KenKenPuzzleQuestion>
    </View>
  );
});

export default KenKenPuzzle;
