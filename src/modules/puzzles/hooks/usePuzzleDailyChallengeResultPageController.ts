import { getDefaultPuzzleDate } from 'modules/puzzles/utils/puzzleUtils';
import useGetDailyPuzzle from 'modules/puzzles/hooks/queries/useGetDailyPuzzle';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';

const useCrossMathPuzzleResultPageController = ({
  puzzleDate = getDefaultPuzzleDate(),
  puzzleType = PUZZLE_TYPES.CROSS_MATH_PUZZLE,
}: {
  puzzleDate?: string | string[];
  puzzleType?: string;
} = EMPTY_OBJECT) => {
  const { puzzle, loading, error } = useGetDailyPuzzle({
    date: puzzleDate,
    fetchPolicy: 'network-only',
    puzzleType: puzzleType,
  });

  return {
    puzzle,
    loading,
    error,
  };
};

export default useCrossMathPuzzleResultPageController;
