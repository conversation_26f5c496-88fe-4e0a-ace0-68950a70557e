import { getDateStringFromTimestamp } from 'core/utils/general';
import { formatDateStringToReadableString } from 'core/utils/dateUtils';

const usePuzzleDates = ({ date }: { date: string | string[] }) => {
  const isToday = date === getDateStringFromTimestamp(getCurrentTime());
  const readableDate = formatDateStringToReadableString(date);

  return {
    readableDate,
    isToday,
  };
};

export default usePuzzleDates;
