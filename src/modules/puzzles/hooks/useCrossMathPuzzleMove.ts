import { useCallback } from 'react';
import { cellType, footerType } from 'modules/puzzles/types/crossMathCellType';
import _map from 'lodash/map';
import _get from 'lodash/get';

const useCrossMathPuzzleMove = ({
  gridItems,
  setGridItems,
  setFooterItems,
}: {
  gridItems: cellType[];
  setGridItems: any;
  setFooterItems: any;
} = EMPTY_OBJECT) => {
  const moveFooterItemToGrid = useCallback(
    ({
      gridCellIndex,
      footerItem,
    }: {
      gridCellIndex: number;
      footerItem: footerType;
    }) => {
      if (_get(footerItem, 'isFilled')) return;
      const oldItemInCell = gridItems[gridCellIndex];
      setGridItems((prevGridItems: any) => {
        const newItems = [...prevGridItems];
        newItems[gridCellIndex] = {
          ...prevGridItems[gridCellIndex],
          value: _get(footerItem, 'value'),
          footerId: _get(footerItem, 'id'),
        };
        return newItems;
      });

      setFooterItems((prevFooterItems) =>
        _map(prevFooterItems, (item) => {
          const newFooterItem = { ...item };
          if (_get(item, 'id') === _get(footerItem, 'id')) {
            newFooterItem.isFilled = true;
          }
          if (
            _get(oldItemInCell, 'value') !== '' &&
            _get(oldItemInCell, 'footerId') === _get(newFooterItem, 'id')
          ) {
            newFooterItem.isFilled = false;
          }
          return newFooterItem;
        }),
      );
    },
    [gridItems, setFooterItems, setGridItems],
  );

  const moveGridItemToFooter = useCallback(
    ({ gridCellIndex }: { gridCellIndex: number }) => {
      const currentGridCellItem = { ...gridItems[gridCellIndex] };
      if (!currentGridCellItem) {
        return;
      }
      if (
        _get(currentGridCellItem, 'value') === '' ||
        !_get(currentGridCellItem, 'editable')
      ) {
        return;
      }
      setGridItems((prevGridItems: any) => {
        const newItems = [...prevGridItems];
        newItems[gridCellIndex].value = '';
        return newItems;
      });
      setFooterItems((prevFooterItems: any) =>
        _map(prevFooterItems, (item) => ({
          ...item,
          isFilled:
            _get(item, 'id') === _get(currentGridCellItem, 'footerId')
              ? false
              : _get(item, 'isFilled', false),
        })),
      );
    },
    [gridItems, setFooterItems, setGridItems],
  );

  return {
    moveFooterItemToGrid,
    moveGridItemToFooter,
  };
};

export default useCrossMathPuzzleMove;
