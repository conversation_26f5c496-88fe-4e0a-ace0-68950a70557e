import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import _map from 'lodash/map';
import uuid from 'react-native-uuid';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import _isEmpty from 'lodash/isEmpty';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useUserActivityTracker from '@/src/core/hooks/useUserActivityTracker';
import ACTIVITY_TYPES from '@/src/core/constants/activityTypes';
import _isNil from 'lodash/isNil';
import arrow_whoosh from 'assets/audio/arrow_whoosh.wav';
import useSound from 'core/hooks/useSound';
import useCrossMathPuzzleMove from 'modules/puzzles/hooks/useCrossMathPuzzleMove';
import _find from 'lodash/find';
import _every from 'lodash/every';
import getPuzzleArrayFromGrid from '../utils/getPuzzleArrayFromGrid';
import { evaluateCrossMathPuzzle } from '../utils/evaluatePuzzle';
import {
  cellType,
  CrossMathCellType,
  footerType,
} from '../types/crossMathCellType';

import { CROSS_MATH_PUZZLE_ACTIONS } from '../constants/puzzleConstants';
import useCrossMathPuzzleHistory, {
  CROSS_MATH_PUZZLE_MOVE_DIRECTIONS,
} from './useCrossMathPuzzleHistory';
import { getNewPuzzle } from '../utils/puzzleGenerator';
import _get from 'lodash/get';

const useCrossMathPuzzleContextState = ({
  gridSize,
  hintsCount,
} = EMPTY_OBJECT) => {
  const { grid: puzzleCells, availableAnswers: availableAnswersNumber } =
    getNewPuzzle({
      gridSize,
      numOfHints: hintsCount,
    });
  // states
  const [gridItems, setGridItems] = useState<cellType[]>([]);
  const [hasSolved, setHasSolved] = useState<boolean>(false);
  const [incorrectAttemptCount, setIncorrectAttemptCount] = useState<number>(0);
  const [isValidSolution, setIsValidSolution] = useState(true);
  const [startTime, setStartTime] = useState(() => getCurrentTimeWithOffset());
  const [selectedGridCell, setSelectedGridCell] = useState<number | null>(null);
  const [selectedFooterCell, setSelectedFooterCell] =
    useState<footerType | null>(null);
  const [footerItems, setFooterItems] = useState<footerType[]>([]);
  const [isReady, setIsReady] = useState<boolean>(false);

  // hooks
  const { playSound } = useSound({ soundFile: arrow_whoosh });
  const { updateActivity } = useUserActivityTracker();

  const { moveGridItemToFooter, moveFooterItemToGrid } = useCrossMathPuzzleMove(
    {
      gridItems,
      setGridItems,
      setFooterItems,
    },
  );

  const { undo, redo, canRedo, canUndo, savePuzzleMove, resetUndoHistory } =
    useCrossMathPuzzleHistory({
      moveGridItemToFooter,
      setSelectedFooterCell,
      setSelectedGridCell,
      moveFooterItemToGrid,
    });

  const isPuzzleFilled = useMemo(
    () => _every(footerItems, (item) => _get(item, 'isFilled', false)),
    [footerItems],
  );

  const dimension = useMemo(() => ({ rows: 7, columns: 7 }), []);

  const resetSelectedItems = useCallback(() => {
    setSelectedGridCell(null);
    setSelectedFooterCell(null);
  }, []);

  const availableAnswers = useMemo(
    () =>
      _map(availableAnswersNumber, (ans) => ({
        id: uuid.v4().toString(),
        type: CrossMathCellType.Operand,
        value: ans,
        isFilled: false,
      })),
    [availableAnswersNumber],
  );

  const initialisePuzzleData = useCallback(() => {
    const listOfItems = getPuzzleArrayFromGrid(puzzleCells);
    setFooterItems(availableAnswers);
    setGridItems(listOfItems);
    setStartTime(getCurrentTimeWithOffset());

    resetUndoHistory();
    setIsReady(true);
  }, [puzzleCells, availableAnswers, resetUndoHistory]);

  const refreshPuzzle = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.CROSS_MATH_PUZZLE.CLICKED_ON_CLEAR_PUZZLE);
    resetSelectedItems();
    const listOfItems = getPuzzleArrayFromGrid(puzzleCells);

    setFooterItems(availableAnswers);
    setGridItems(listOfItems);

    resetUndoHistory();
  }, [resetSelectedItems, availableAnswers, resetUndoHistory]);

  const gridItemsRef = useRef(gridItems);
  gridItemsRef.current = gridItems;

  const checkPuzzle = useCallback(() => {
    if (_isEmpty(gridItemsRef.current) || !isPuzzleFilled) {
      return;
    }

    const isValid = evaluateCrossMathPuzzle(gridItemsRef.current, dimension);
    setIsValidSolution(isValid);
    if (isValid) {
      const timeSpent = getCurrentTimeWithOffset() - startTime;
      if (!hasSolved) {
        updateActivity({
          activityType: ACTIVITY_TYPES.DAILY_PUZZLE,
          duration: timeSpent,
        });
      }
      setHasSolved(isValid);
    } else {
      setIncorrectAttemptCount((prev: number) => prev + 1);
    }
  }, [dimension, isPuzzleFilled, hasSolved, startTime, updateActivity]);

  const initialisePuzzleDataRef = useRef(initialisePuzzleData);
  initialisePuzzleDataRef.current = initialisePuzzleData;

  const handleDrop = useCallback(
    (gridCellIndex: number, footerItem: footerType) => {
      const oldItemInCell = gridItems[gridCellIndex];
      if (_isNil(oldItemInCell)) return;
      if (!_get(oldItemInCell, 'editable')) return;
      if (_get(footerItem, 'isFilled')) return;

      playSound();
      moveFooterItemToGrid({ gridCellIndex, footerItem });
      resetSelectedItems();
      savePuzzleMove({
        gridCellIndex,
        footerItem,
        direction: CROSS_MATH_PUZZLE_MOVE_DIRECTIONS.FOOTER_TO_GRID,
      });
    },
    [
      gridItems,
      moveFooterItemToGrid,
      playSound,
      resetSelectedItems,
      savePuzzleMove,
    ],
  );

  const onTapGridCell = useCallback(
    ({ index }: { index: number }) => {
      if (_isNil(selectedFooterCell)) {
        // if item is filled, then move it back to footer
        const item = gridItems[index];
        const footerItem = _find(footerItems, { id: _get(item, 'footerId') });
        if (_get(item, 'value') !== '' && _get(item, 'editable')) {
          moveGridItemToFooter({ gridCellIndex: index });
          savePuzzleMove({
            gridCellIndex: index,
            footerItem: {
              ...footerItem,
              isFilled: false,
            },
            direction: CROSS_MATH_PUZZLE_MOVE_DIRECTIONS.GRID_TO_FOOTER,
          });
          return;
        }
        setSelectedGridCell(index);
        return;
      }
      handleDrop(index, selectedFooterCell);
      setSelectedFooterCell(null);
    },
    [
      selectedFooterCell,
      handleDrop,
      gridItems,
      footerItems,
      moveGridItemToFooter,
      savePuzzleMove,
    ],
  );

  const onTapFooterCell = useCallback(
    (footerCell: footerType) => {
      if (_isNil(selectedGridCell)) {
        setSelectedFooterCell(footerCell);
        return;
      }
      handleDrop(selectedGridCell, footerCell);
      setSelectedGridCell(null);
    },
    [handleDrop, selectedGridCell, setSelectedFooterCell, setSelectedGridCell],
  );

  const checkPuzzleRef = useRef(checkPuzzle);
  checkPuzzleRef.current = checkPuzzle;

  useEffect(() => {
    if (isPuzzleFilled) {
      checkPuzzleRef.current();
    }
  }, [isPuzzleFilled]);

  const state = useMemo(
    () => ({
      gridItems,
      startTime,
      dimension,
      footerItems,
      selectedFooterCell,

      // solved correctness
      hasSolved,
      isPuzzleFilled,
      isIncorrectSolution: isPuzzleFilled && !isValidSolution,
      incorrectAttemptCount,

      selectedGridCell,
      canRedo,
      canUndo,
    }),
    [
      canRedo,
      canUndo,
      dimension,
      isPuzzleFilled,
      footerItems,
      gridItems,
      hasSolved,
      incorrectAttemptCount,
      isValidSolution,
      selectedFooterCell,
      selectedGridCell,
      startTime,
    ],
  );

  const onAction = useCallback(
    ({ type, payload }: { type: string; payload: any }) => {
      switch (type) {
        case CROSS_MATH_PUZZLE_ACTIONS.UNDO:
          setIsValidSolution(true);
          undo();
          break;
        case CROSS_MATH_PUZZLE_ACTIONS.REDO:
          setIsValidSolution(true);
          redo();
          break;
        case CROSS_MATH_PUZZLE_ACTIONS.CLEAR:
          setIsValidSolution(true);
          refreshPuzzle();
          break;
        case CROSS_MATH_PUZZLE_ACTIONS.HINT:
          // handle hint
          break;
        case CROSS_MATH_PUZZLE_ACTIONS.CHECK_PUZZLE:
          checkPuzzle();
          break;
        case CROSS_MATH_PUZZLE_ACTIONS.TAP_FOOTER_ITEM:
          onTapFooterCell(payload);
          break;
        case CROSS_MATH_PUZZLE_ACTIONS.TAP_GRID_ITEM:
          setIsValidSolution(true);
          onTapGridCell(payload);
          break;
        default:
          break;
      }
    },
    [undo, checkPuzzle, redo, refreshPuzzle, onTapFooterCell, onTapGridCell],
  );

  return {
    state,
    onAction,
  };
};

export default useCrossMathPuzzleContextState;
