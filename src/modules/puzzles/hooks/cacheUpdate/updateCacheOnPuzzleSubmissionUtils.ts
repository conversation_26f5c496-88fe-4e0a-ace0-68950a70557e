import { GET_DAILY_PUZZLE_QUERY } from 'modules/puzzles/hooks/queries/useGetDailyPuzzle';
import _compact from 'lodash/compact';
import _map from 'lodash/map';
import _includes from 'lodash/includes';
import _get from 'lodash/get';
import _set from 'lodash/set';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import { getMonthYearStringFromDateString } from '../../utils/puzzleCalendar';
import { GET_PUZZLE_SUBMISSIONS_BY_MONTH_QUERY } from '../queries/useGetPuzzleSubmissionsByMonth';

export const updateDailyPuzzleCache = ({
  cache,
  puzzleSubmissionResult,
  puzzleDate,
}: {
  cache: any;
  puzzleSubmissionResult: any;
  puzzleDate: string;
}) => {
  try {
    const existingPuzzle = cache.readQuery({
      query: GET_DAILY_PUZZLE_QUERY,
      variables: { date: puzzleDate },
    });

    if (existingPuzzle && existingPuzzle.getDailyPuzzleByType) {
      cache.writeQuery({
        query: GET_DAILY_PUZZLE_QUERY,
        variables: { date: puzzleDate },
        data: {
          getDailyPuzzleByType: {
            ...existingPuzzle.getDailyPuzzleByType,
            hasAttempted: true,
            currentUserResult: puzzleSubmissionResult,
          },
        },
      });
    }
  } catch (error) {
    Analytics.track(
      ANALYTICS_EVENTS.CROSS_MATH_PUZZLE.ERROR_ON_UPDATE_DAILY_PUZZLE_CACHE,
      { error },
    );
  }
};

export const updatePuzzleSubmissionByMonthCache = ({
  cache,
  puzzleSubmissionResult,
  puzzleDate,
  puzzleType = PUZZLE_TYPES.CROSS_MATH_PUZZLE,
}: {
  cache: any;
  puzzleSubmissionResult: any;
  puzzleDate: string;
  puzzleType?: string;
}) => {
  try {
    // update PuzzleSubmissionByMonthCache
    const yearMonthString = getMonthYearStringFromDateString(puzzleDate);
    const existingPuzzleSubmissionByMonth = cache.readQuery({
      query: GET_PUZZLE_SUBMISSIONS_BY_MONTH_QUERY,
      variables: { yearMonths: [yearMonthString] },
    });

    if (
      !existingPuzzleSubmissionByMonth?.getPuzzleSubmissionsByMonthByType
        ?.length
    ) {
      cache.writeQuery({
        query: GET_PUZZLE_SUBMISSIONS_BY_MONTH_QUERY,
        variables: { yearMonths: [yearMonthString], puzzleType },
        data: {
          getPuzzleSubmissionsByMonthByType: [
            {
              __typename: 'PuzzleMonthlySubmissionReport',
              yearMonth: yearMonthString,
              puzzleSubmissions: [
                {
                  __typename: 'PuzzleResult',
                  puzzleId: puzzleSubmissionResult.puzzleId,
                  timeSpent: puzzleSubmissionResult.timeSpent,
                  puzzleDate,
                },
              ],
            },
          ],
        },
      });
      return;
    }

    const existingPuzzleSubmissions =
      _get(
        existingPuzzleSubmissionByMonth,
        ['getPuzzleSubmissionsByMonthByType', 0, 'puzzleSubmissions'],
        EMPTY_ARRAY,
      ) || EMPTY_ARRAY;

    // check if puzzle submission already exists
    if (
      _includes(
        _map(existingPuzzleSubmissions, 'puzzleId'),
        _get(puzzleSubmissionResult, 'puzzleId'),
      )
    ) {
      return;
    }

    const newSubmission = {
      __typename: 'PuzzleResult',
      puzzleId: puzzleSubmissionResult.puzzleId,
      timeSpent: puzzleSubmissionResult.timeSpent,
      puzzleDate,
    };

    if (existingPuzzleSubmissionByMonth) {
      const udpatedData = _set(
        existingPuzzleSubmissionByMonth,
        ['getPuzzleSubmissionsByMonthByType', 0, 'puzzleSubmissions'],
        _compact([...existingPuzzleSubmissions, newSubmission]),
      );

      cache.writeQuery({
        query: GET_PUZZLE_SUBMISSIONS_BY_MONTH_QUERY,
        variables: { yearMonths: [yearMonthString] },
        data: {
          getPuzzleSubmissionsByMonthByType: udpatedData,
        },
      });
    }
  } catch (error) {
    Analytics.track(
      ANALYTICS_EVENTS.CROSS_MATH_PUZZLE
        .ERROR_ON_UPDATE_PUZZLE_SUBMISSIONS_BY_MONTH_CACHE,
      { error },
    );
  }
};
