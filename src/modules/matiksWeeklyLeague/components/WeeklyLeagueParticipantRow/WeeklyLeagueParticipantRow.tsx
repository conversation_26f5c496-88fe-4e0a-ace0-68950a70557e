import React, { useMemo } from 'react';
import { Text, View } from 'react-native';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import _isNil from 'lodash/isNil';
import _get from 'lodash/get';
import _isEqual from 'lodash/isEqual';
import dark from '@/src/core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';
import userReader from 'core/readers/userReader';
import SecondRankSvg from '@/src/components/svg/Ranks/SecondRankSvg';
import FirstRankSvg from '@/src/components/svg/Ranks/FirstRankSvg';
import ThirdRankSvg from '@/src/components/svg/Ranks/ThirdRank';
import UserImage from 'atoms/UserImage/UserImage';
import styles from './WeeklyLeagueParticipantRow.style';

const getRankIcon = (rank: number) => {
  switch (rank) {
    case 1:
      return FirstRankSvg;
    case 2:
      return SecondRankSvg;
    case 3:
      return ThirdRankSvg;
    default:
      return null;
  }
};

const WeeklyLeagueParticipantRow = ({
  participant,
  isInPromotionZone,
}: {
  participant: any;
  isInPromotionZone?: boolean;
}) => {
  const { user: currentSessionUser } = useSession();
  const { isMobile: isCompactMode } = useMediaQuery();

  const isSameUser = _isEqual(
    _get(participant, ['user', 'username'], ''),
    userReader.username(currentSessionUser),
  );

  const isPromotionZoneUser = !_isNil(isInPromotionZone) && isInPromotionZone;
  const isDemotionZoneUser = !_isNil(isInPromotionZone) && !isInPromotionZone;

  const additionalStyles = useMemo(() => {
    if (isSameUser && isDemotionZoneUser)
      return { color: dark.colors.demotionText };
    if (isSameUser && isPromotionZoneUser)
      return { color: dark.colors.secondary };

    return {};
  }, [isSameUser, isPromotionZoneUser, isDemotionZoneUser]);

  const renderRankSection = ({ rank }: { rank: number }) => {
    const RankIcon = getRankIcon(rank);
    if (_isNil(RankIcon)) {
      return (
        <Text style={[styles.rankText, additionalStyles]}>
          {participant?.rank}
        </Text>
      );
    }
    return <RankIcon />;
  };

  if (_isNil(participant)) {
    return null;
  }

  return (
    <View
      style={[
        styles.container,
        !isCompactMode && { borderRadius: 25 },
        isSameUser && { backgroundColor: dark.colors.gradientBackground },
      ]}
      key={`${participant?.rank}`}
    >
      <View style={styles.rankContainer}>
        {renderRankSection({ rank: participant?.rank })}
      </View>

      <View style={styles.userInfoContainer}>
        <UserImage user={participant?.user} style={styles.userImage} />
        <Text style={[styles.nameText, additionalStyles]}>
          {userReader.username(participant?.user)}
        </Text>
      </View>
      <View style={styles.userXPContainer}>
        <Text
          style={[styles.xPText, additionalStyles]}
        >{`${participant?.statikCoins} XP`}</Text>
      </View>
    </View>
  );
};

export default React.memo(WeeklyLeagueParticipantRow);
