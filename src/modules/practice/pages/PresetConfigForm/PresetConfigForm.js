import React, { useCallback, useMemo } from 'react';
import { ScrollView, Text, View } from 'react-native';
import PrimaryButton from 'atoms/PrimaryButton';
import { useRouter } from 'expo-router';

import useFormFields from 'shared/Form/hooks/useFormFields';
import FormField from 'shared/Form/FormField';
import _get from 'lodash/get';
import _map from 'lodash/map';
import { PRACTICE_LEFT_PANE_TABS_CONFIGS } from '../Practice/components/OperatorSelector/constants/practice';
import { getPracticeFormFields } from '../../utils/configForms';
import { usePracticeContext } from '../../../../../app/_layout';
import useNthRootConfigurationFormStyles from './PresetConfigForm.style';

const PresetConfigForm = ({ category }) => {
  const formFields = useMemo(
    () => getPracticeFormFields({ category }),
    [category],
  );

  const {
    formState,
    errors,
    visibleFields,
    updateFormState,
    validateFormData,
    enableValidation,
  } = useFormFields({ formFields });

  const styles = useNthRootConfigurationFormStyles();

  const router = useRouter();

  const { setPracticeConfig } = usePracticeContext();
  const onPracticePressed = useCallback(() => {
    const isValid = validateFormData();
    const preset = { categoryId: category, config: formState };
    if (isValid) {
      setPracticeConfig({ selectedPresets: [preset] });
      router.push(`/nets/${category}`);
    } else {
      enableValidation();
    }
  }, [setPracticeConfig, formState, category]);

  const renderFormField = useCallback(
    (field) => {
      const { key, type } = field;
      const value = _get(formState, [key]);
      const error = _get(errors, [key]);
      return (
        <View key={field.key} style={styles.formFieldContainer}>
          <FormField
            field={field}
            value={value}
            error={error}
            onValueChange={(value) => updateFormState({ key, value })}
          />
        </View>
      );
    },
    [updateFormState, errors, formState],
  );

  const { operationName } = PRACTICE_LEFT_PANE_TABS_CONFIGS[category];

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.innerContainer}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      >
        <Text style={styles.headerText}>{operationName}</Text>
        {_map(visibleFields, renderFormField)}
        <View
          style={{
            width: '100%',
            alignItems: 'flex-end',
            marginVertical: 15,
            marginTop: 50,
          }}
        >
          <PrimaryButton
            label="Practice"
            radius="xl"
            buttonStyle={{
              height: 35,
              justifyContent: 'center',
              padding: 0,
              width: 130,
            }}
            labelStyle={{ fontSize: 14, fontFamily: 'Montserrat-600' }}
            onPress={onPracticePressed}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default React.memo(PresetConfigForm);
