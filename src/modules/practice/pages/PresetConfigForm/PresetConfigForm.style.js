import { useMemo } from "react";
import { StyleSheet } from "react-native";
import useMediaQuery from 'core/hooks/useMediaQuery'
import dark from "../../../../core/constants/themes/dark";

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {
        flex: isCompactMode ? 8 : 5,
    },
    innerContainer: {
        paddingTop: isCompactMode ? 16 : 36,
        paddingHorizontal: isCompactMode ? 16 : 24,
    },
    headerText: {
        color: dark.colors.textDark,
        lineHeight: 20,
        fontSize: 14,
        fontFamily: 'Montserrat-600',
        marginBottom:20
    },
    formFieldContainer: {
        paddingVertical: 16,
        borderColor: dark.colors.tertiary,
        borderBottomWidth: 1,
    }
})

const useNthRootConfigurationFormStyles = () => {
    const { isMobile } = useMediaQuery();

    const styles = useMemo(() => createStyles(isMobile), [isMobile]);

    return styles;
};

export default useNthRootConfigurationFormStyles
