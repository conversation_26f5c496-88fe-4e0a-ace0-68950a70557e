import AdditionIcon from '@/assets/images/practice/addition.png';
import MultiplicationIcon from '@/assets/images/practice/multiplication.png';
import RecentIcon from '@/assets/images/practice/recent.png';
import FlashAnzanIcon from '@/assets/images/practice/flash_anzan.png';
import DivisonIcon from '@/assets/images/practice/division.png';
import NthRootIcon from 'assets/images/practice/nthRoot.png';
import LcmIcon from 'assets/images/practice/lcm.png';
import HcfIcon from 'assets/images/practice/hcf.png';
import ExponentIcon from 'assets/images/practice/exponents.png';

import { QUESTION_CATEGORIES } from 'core/constants/questionCategories';

export const PRACTICE_CATEGORIES = QUESTION_CATEGORIES;
export const DMAS_CATEGORIES = [
  PRACTICE_CATEGORIES.MULTIPLICATION,
  PRACTICE_CATEGORIES.DIVISION,
  PRACTICE_CATEGORIES.ADD_AND_SUBSTRACT,
];

export const PRACTICE_LEFT_PANE_TABS = {
  ...PRACTICE_CATEGORIES,
  RECENT_AND_SAVED: 'RECENT_AND_SAVED',
};

export const PRACTICE_LEFT_PANE_TABS_ORDERED_LIST = [
  PRACTICE_LEFT_PANE_TABS.RECENT_AND_SAVED,
  PRACTICE_LEFT_PANE_TABS.ADD_AND_SUBSTRACT,
  PRACTICE_LEFT_PANE_TABS.MULTIPLICATION,
  PRACTICE_LEFT_PANE_TABS.DIVISION,
  PRACTICE_LEFT_PANE_TABS.FLASH_ANZAN,
  PRACTICE_LEFT_PANE_TABS.ROOT,
  PRACTICE_LEFT_PANE_TABS.EXPONENT,
  PRACTICE_LEFT_PANE_TABS.HCF,
  PRACTICE_LEFT_PANE_TABS.LCM,
  PRACTICE_LEFT_PANE_TABS.MOD,
];

export const PRACTICE_LEFT_PANE_TABS_CONFIGS = {
  [PRACTICE_LEFT_PANE_TABS.RECENT_AND_SAVED]: {
    operationName: 'Recently & Saved',
    icon: RecentIcon,
    id: PRACTICE_LEFT_PANE_TABS.RECENT_AND_SAVED,
    categoryId: null,
    tag: PRACTICE_LEFT_PANE_TABS.RECENT_AND_SAVED,
  },
  [PRACTICE_LEFT_PANE_TABS.ADD_AND_SUBSTRACT]: {
    operationName: 'Addition & Subtraction',
    icon: AdditionIcon,
    id: PRACTICE_LEFT_PANE_TABS.ADD_AND_SUBSTRACT,
    categoryId: PRACTICE_CATEGORIES.ADD_AND_SUBSTRACT,
    tag: PRACTICE_LEFT_PANE_TABS.ADD_AND_SUBSTRACT,
  },
  [PRACTICE_LEFT_PANE_TABS.MULTIPLICATION]: {
    operationName: 'Multiplication',
    icon: MultiplicationIcon,
    id: PRACTICE_LEFT_PANE_TABS.MULTIPLICATION,
    categoryId: PRACTICE_CATEGORIES.MULTIPLICATION,
    tag: PRACTICE_LEFT_PANE_TABS.MULTIPLICATION,
  },
  [PRACTICE_LEFT_PANE_TABS.DIVISION]: {
    operationName: 'Division',
    icon: DivisonIcon,
    id: PRACTICE_LEFT_PANE_TABS.DIVISION,
    categoryId: PRACTICE_CATEGORIES.DIVISION,
    tag: PRACTICE_CATEGORIES.DIVISION,
  },

  [PRACTICE_LEFT_PANE_TABS.FLASH_ANZAN]: {
    operationName: 'Flash Anzan',
    icon: FlashAnzanIcon,
    id: PRACTICE_LEFT_PANE_TABS.FLASH_ANZAN,
    categoryId: PRACTICE_CATEGORIES.FLASH_ANZAN,
    tag: PRACTICE_CATEGORIES.FLASH_ANZAN,
  },
  [PRACTICE_LEFT_PANE_TABS.ROOT]: {
    operationName: 'Nth Root',
    icon: NthRootIcon,
    id: PRACTICE_LEFT_PANE_TABS.ROOT,
    categoryId: PRACTICE_CATEGORIES.ROOT,
    tag: PRACTICE_CATEGORIES.ROOT,
  },
  [PRACTICE_LEFT_PANE_TABS.EXPONENT]: {
    operationName: 'Exponent',
    icon: ExponentIcon,
    id: PRACTICE_LEFT_PANE_TABS.EXPONENT,
    categoryId: PRACTICE_CATEGORIES.EXPONENT,
    tag: PRACTICE_CATEGORIES.EXPONENT,
  },
  [PRACTICE_LEFT_PANE_TABS.HCF]: {
    operationName: 'HCF (A,B)',
    icon: HcfIcon,
    id: PRACTICE_LEFT_PANE_TABS.HCF,
    categoryId: PRACTICE_CATEGORIES.HCF,
    tag: PRACTICE_CATEGORIES.HCF,
  },
  [PRACTICE_LEFT_PANE_TABS.LCM]: {
    operationName: 'LCM (A,B)',
    icon: LcmIcon,
    id: PRACTICE_LEFT_PANE_TABS.LCM,
    categoryId: PRACTICE_CATEGORIES.LCM,
    tag: PRACTICE_CATEGORIES.LCM,
  },
  [PRACTICE_LEFT_PANE_TABS.MOD]: {
    operationName: 'Mod',
    icon: DivisonIcon,
    id: PRACTICE_LEFT_PANE_TABS.MOD,
    categoryId: PRACTICE_CATEGORIES.MOD,
    tag: PRACTICE_CATEGORIES.MOD,
  },
};
