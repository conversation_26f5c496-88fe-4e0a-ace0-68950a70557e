import { PRACTICE_CATEGORIES } from '../../pages/Practice/components/OperatorSelector/constants/practice';

import { getNthRootFormFields } from './nthRootFormFields';
import { getExponentFormFields } from './exponentFormFields';
import { getModFormFields } from './modFormFields';
import { getHcfFormFields } from './hcfFormFields';
import { getLcmFormFields } from './lcmFormFields';

export const getFlashAnzanFormFields = () => {};

const CATEGORY_VS_FORM_FIELDS_GETTER = {
  [PRACTICE_CATEGORIES.FLASH_ANZAN]: getFlashAnzanFormFields,
  [PRACTICE_CATEGORIES.ROOT]: getNthRootFormFields,
  [PRACTICE_CATEGORIES.EXPONENT]: getExponentFormFields,
  [PRACTICE_CATEGORIES.HCF]: getHcfFormFields,
  [PRACTICE_CATEGORIES.LCM]: getLcmFormFields,
  [PRACTICE_CATEGORIES.MOD]: getModFormFields,
};

export const getPracticeFormFields = ({ category }) =>
  CATEGORY_VS_FORM_FIELDS_GETTER[category]?.() ?? EMPTY_ARRAY;
