import { FORM_INPUT_TYPES } from '@/src/core/constants/forms';
import { HCF_FIELDS_KEYS } from '../../constants/fieldKeys';

export const getHcfFormFields = () => [
  {
    key: HCF_FIELDS_KEYS.FIRST_NO_DIGITS,
    type: FORM_INPUT_TYPES.RANGE_SELECTOR,
    label: 'Select Range (A)',
    required: true,
    visible: true,
    defaultValue: {
      min: 1,
      max: 10,
    },
    rules: {
      minValue: 1,
      maxValue: 1000,
    },
    additional: {
      incrementBy: 1,
    },
  },
  {
    key: HCF_FIELDS_KEYS.SECOND_NO_DIGITS,
    type: FORM_INPUT_TYPES.RANGE_SELECTOR,
    label: 'Select Range (B)',
    required: true,
    visible: true,
    defaultValue: {
      min: 1,
      max: 10,
    },
    rules: {
      minValue: 1,
      maxValue: 1000,
    },
    additional: {
      incrementBy: 1,
    },
  },
  {
    key: HCF_FIELDS_KEYS.NUMBER_OF_QUESTIONS,
    type: FORM_INPUT_TYPES.NUMBER_INPUT,
    label: 'Number of questions',
    required: true,
    visible: true,
    defaultValue: 2,
    rules: {
      minValue: 2,
      maxValue: 10,
    },
    additional: {
      incrementBy: 1,
    },
  },
];
