/* eslint-disable no-undef */
export type WebsocketState = {
  ws: WebSocket | null;
  token: string;
  isConnected: boolean;
  lastMessage: Record<string, any>;
  channels: Set<string>;
  cachedMessageQueue: any[];
  reconnectCount: number;
  reconnectTimeout: number;
  pingPongTimeout: number;
  connect: (_token: string, _onMessage?: (_event: any) => void) => void;
  disconnect: () => void;
  joinChannel: (_channel: string) => void;
  leaveChannel: (_channel: string) => void;
  sendMessage: (_message: any) => void;
  reconnect: () => void;
  isConnecting: () => boolean;
  cleanUp: () => void;
  close: () => void;
  updateLastMessage: (_channel: string, _message: any) => void;
};

export type SetWebsocketStore = (
  _state: (_webSocketState: WebsocketState) => WebsocketState,
) => void;
export type GetWebsocketStore = () => WebsocketState;
