/* eslint-disable prefer-destructuring */
/* eslint-disable no-undef */
/* eslint-disable import/no-unused-modules */
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import { WS_URL_2 } from '../../core/constants/graphql';
import { GetWebsocketStore, SetWebsocketStore } from './types';

interface WebsocketStoreHandlers {
  connect: (_token: string, _onMessage?: (_event: any) => void) => void;
  disconnect: () => void;
  joinChannel: (_channel: string) => void;
  leaveChannel: (_channel: string) => void;
  sendMessage: (_message: any) => void;
  reconnect: () => void;
  isConnecting: () => boolean;
  cleanUp: () => void;
  close: () => void;
  updateLastMessage: (_channel: string, _message: any) => void;
}

const LINEAR_ATTEMPT_THRESHOLD = 50;
const RECONNECT_INTERVAL = 2000;
let timeOut: any | null = null;

export default class WebsocketHandlers implements WebsocketStoreHandlers {
  private set: SetWebsocketStore;

  private get: GetWebsocketStore;

  private onMessage?: (_event: any) => void;

  constructor(set: SetWebsocketStore, get: GetWebsocketStore) {
    this.set = set;
    this.get = get;
  }

  connect(token: string, onMessage?: (_event: any) => void) {
    let _token = token;

    this.set((state) => {
      const _ws = state.ws;
      if (
        _ws &&
        (_ws.readyState === WebSocket.OPEN ||
          _ws.readyState === WebSocket.CONNECTING)
      ) {
        return state;
      }

      if (!_token) {
        _token = state.token;
      }

      const wsUrl = `${WS_URL_2}?token=${_token}`;
      const ws = new WebSocket(wsUrl);
      ws.onopen = this.onOpen;
      ws.onclose = this.onClose;
      ws.onerror = this.onError;
      if (onMessage) {
        this.onMessage = onMessage;
      }
      if (this.onMessage) {
        ws.onmessage = this.onMessage;
      }

      return {
        ...state,
        token: _token,
        ws,
      };
    });
  }

  close() {
    const ws = this.get().ws;
    if (
      ws &&
      (ws.readyState === WebSocket.OPEN ||
        ws.readyState === WebSocket.CONNECTING)
    ) {
      ws.close();
      printDebug('WebSocket close requested');
    } else {
      printDebug('WebSocket already closed or closing');
    }
  }

  isConnecting() {
    const ws = this.get().ws;
    return ws !== null && ws.readyState === WebSocket.CONNECTING;
  }

  processCachedMessages() {
    const cachedMessages = this.get().cachedMessageQueue;
    const ws = this.get().ws;
    if (!ws || ws.readyState !== WebSocket.OPEN || !cachedMessages.length) {
      return;
    }
    cachedMessages.forEach((message: any) => {
      this.sendMessage(message);
    });
    this.set((state) => ({
      ...state,
      cachedMessageQueue: [],
    }));
  }

  updateLastMessage(channel: string, message: any) {
    this.set((state) => ({
      ...state,
      lastMessage: {
        ...state.lastMessage,
        [channel]: message,
      },
    }));
  }

  sendMessage(message: string | object) {
    const ws = this.get().ws;
    if (ws && ws.readyState === WebSocket.OPEN) {
      if (typeof message === 'object') {
        ws.send(JSON.stringify(message));
      } else if (typeof message === 'string') {
        ws.send(message);
      } else {
        throw new Error('Invalid message type');
      }
    } else {
      this.set((state) => ({
        ...state,
        cachedMessageQueue: [...state.cachedMessageQueue, message],
      }));
    }
  }

  disconnect() {
    this.set((state) => ({
      ...state,
      ws: null,
    }));
  }

  joinChannel(channel: string) {
    const ws = this.get().ws;
    if (ws && ws.readyState === WebSocket.OPEN) {
      this.sendMessage({
        type: 'channel_subscribe',
        channel,
      });
    } else {
      this.set((state) => ({
        ...state,
        cachedMessageQueue: [
          ...state.cachedMessageQueue,
          {
            type: 'channel_subscribe',
            channel,
          },
        ],
      }));
    }

    this.set((state) => ({
      ...state,
      channels: new Set([...state.channels, channel]),
    }));
  }

  reconnect() {
    const ws = this.get().ws;
    if (
      !ws ||
      ws.readyState === WebSocket.OPEN ||
      ws.readyState === WebSocket.CONNECTING
    )
      return;
    this.connect(this.get().token);
    this.rejoinChannels();
    this.processCachedMessages();
  }

  rejoinChannels() {
    const channels = this.get().channels;
    channels.forEach((channel) => {
      this.joinChannel(channel);
    });
  }

  leaveChannel(channel: string) {
    const ws = this.get().ws;
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(
        JSON.stringify({
          type: 'channel_unsubscribe',
          channel,
        }),
      );
    } else {
      this.set((state) => ({
        ...state,
        cachedMessageQueue: [
          ...state.cachedMessageQueue,
          {
            type: 'channel_unsubscribe',
            channel,
          },
        ],
      }));
    }
  }

  onOpen = () => {
    this.rejoinChannels();
    this.processCachedMessages();
    this.set((state) => ({
      ...state,
      reconnectCount: 0,
      isConnected: true,
    }));
  };

  onClose = () => {
    printDebug('ws closing');
    const { reconnectCount } = this.get();
    this.set((state) => ({
      ...state,
      reconnectCount: state.reconnectCount + 1,
      isConnected: false,
    }));
    if (reconnectCount >= LINEAR_ATTEMPT_THRESHOLD) {
      if (timeOut) {
        clearTimeout(timeOut);
      }
      timeOut = setTimeout(() => {
        this.reconnect();
      }, RECONNECT_INTERVAL);
    } else {
      const delay =
        (10 * RECONNECT_INTERVAL) / (LINEAR_ATTEMPT_THRESHOLD - reconnectCount);

      if (timeOut) {
        clearTimeout(timeOut);
      }
      timeOut = setTimeout(() => {
        this.reconnect();
      }, delay);
    }
  };

  cleanUp() {
    if (timeOut) {
      clearTimeout(timeOut);
    }
    this.get().ws?.close();
    this.set((state) => ({
      ...state,
      ws: null,
      reconnectCount: 0,
      isConnected: false,
    }));
  }

  onError = (error: any) => {
    console.error('WebSocket error:', error);
    Analytics.track(ANALYTICS_EVENTS.WEB_SOCKET.ON_ERROR, { error });
    this.get().ws?.close();
    this.set((state) => ({
      ...state,
      isConnected: false,
    }));
    this.onClose();
  };
}
