import React from 'react';
import { render, waitFor } from '@testing-library/react-native';
import SafeRive, { RiveComponent, RiveErrorBoundary } from '../index';

// Mock the native Rive component
jest.mock('rive-react-native', () => {
  return {
    __esModule: true,
    default: ({ onLoad, onError }: any) => {
      // Simulate successful load after a delay
      setTimeout(() => {
        if (onLoad) onLoad();
      }, 100);
      return null;
    },
    RiveRef: {},
  };
});

// Mock Analytics
jest.mock('core/analytics', () => ({
  track: jest.fn(),
}));

// No additional mocks needed for minimal version

describe('SafeRive Component', () => {
  it('renders without crashing', () => {
    render(
      <SafeRive
        url="https://cdn.rive.app/animations/vehicles.riv"
        artboardName="Artboard"
        stateMachineName="State Machine 1"
      />,
    );
  });

  it('shows loading state initially', () => {
    const { getByText } = render(
      <SafeRive
        url="https://cdn.rive.app/animations/vehicles.riv"
        artboardName="Artboard"
        stateMachineName="State Machine 1"
      />,
    );

    expect(getByText('Loading animation...')).toBeTruthy();
  });

  it('handles custom fallback text', async () => {
    const { getByText } = render(
      <SafeRive
        url="invalid-url"
        fallbackText="Custom fallback message"
      />,
    );

    // Wait for the component to process the invalid URL and show fallback
    await waitFor(() => {
      expect(getByText('Custom fallback message')).toBeTruthy();
    }, { timeout: 2000 });
  });

  it('renders with error boundary protection', () => {
    const result = render(
      <RiveErrorBoundary fallbackText="Error boundary fallback">
        <RiveComponent
          url="https://cdn.rive.app/animations/vehicles.riv"
          artboardName="Artboard"
          stateMachineName="State Machine 1"
        />
      </RiveErrorBoundary>,
    );

    expect(result).toBeTruthy();
  });

  it('handles retry functionality', async () => {
    const { getByText } = render(
      <SafeRive
        url="invalid-url"
        enableRetry={true}
        maxRetries={2}
      />,
    );

    await waitFor(() => {
      expect(getByText('Animation unavailable')).toBeTruthy();
    }, { timeout: 3000 });
  });

  it('respects timeout configuration', () => {
    render(
      <SafeRive
        url="https://cdn.rive.app/animations/vehicles.riv"
        timeout={5000}
      />,
    );

    // Component should render without throwing
    expect(true).toBe(true);
  });
});

describe('RiveComponent (Direct)', () => {
  it('renders without crashing', () => {
    render(
      <RiveComponent
        url="https://cdn.rive.app/animations/vehicles.riv"
        artboardName="Artboard"
        stateMachineName="State Machine 1"
      />,
    );
  });
});

describe('RiveErrorBoundary', () => {
  it('catches and handles errors gracefully', () => {
    const ThrowError = () => {
      throw new Error('Test error');
    };

    const { getByText } = render(
      <RiveErrorBoundary fallbackText="Error caught">
        <ThrowError />
      </RiveErrorBoundary>,
    );

    expect(getByText('Error caught')).toBeTruthy();
  });
});
