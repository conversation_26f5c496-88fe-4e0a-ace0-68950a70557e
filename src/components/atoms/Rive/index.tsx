
import  RiveComponent  from './Rive';
import RiveErrorBoundary from './RiveErrorBoundary';

interface SafeRiveProps {
  url: string;
  resourceName?: string;
  artboardName?: string;
  stateMachineName?: string;
  style?: ViewStyle;
  autoPlay?: boolean;
  loop?: boolean;
  onLoopEnd?: () => void;
  fallbackText?: string;
  enableRetry?: boolean;
  maxRetries?: number;
  timeout?: number;
}

const SafeRive: React.FC<SafeRiveProps> = (props) => {
  const { fallbackText = 'Animation unavailable', style, ...riveProps } = props;

  return (
    <RiveErrorBoundary fallbackText={fallbackText} style={style}>
    <RiveComponent {...riveProps} style={style} />
  </RiveErrorBoundary>
);
};

export default React.memo(SafeRive);
