import React from 'react';
import { ViewStyle, Platform } from 'react-native';
import RiveErrorBoundary from './RiveErrorBoundary';

// Platform-specific imports
let RiveComponent: React.ComponentType<any>;

if (Platform.OS === 'web') {
  RiveComponent = require('./Rive.web').default;
} else {
  RiveComponent = require('./Rive.native').default;
}

interface SafeRiveProps {
  url: string;
  resourceName?: string;
  artboardName?: string;
  stateMachineName?: string;
  style?: ViewStyle;
  autoPlay?: boolean;
  loop?: boolean;
  onLoopEnd?: () => void;
  fallbackText?: string;
  enableRetry?: boolean;
  maxRetries?: number;
  timeout?: number;
}

const SafeRive: React.FC<SafeRiveProps> = (props) => {
  const { fallbackText = 'Animation unavailable', style, ...riveProps } = props;

  // Simple error boundary wrapper - maintains exact same behavior as before
  return (
    <RiveErrorBoundary fallbackText={fallbackText} style={style}>
      <RiveComponent {...riveProps} style={style} />
    </RiveErrorBoundary>
  );
};

export default React.memo(SafeRive);

// Export individual components for advanced usage
export { RiveComponent, RiveErrorBoundary };
