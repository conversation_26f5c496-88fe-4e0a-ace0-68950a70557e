import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Text } from 'react-native';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import dark from 'core/constants/themes/dark';

interface Props {
  children: ReactNode;
  fallbackText?: string;
  style?: any;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

class RiveErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('Rive Error Boundary caught an error:', error);
    
    // Track the error
    Analytics.track(ANALYTICS_EVENTS.COMPONENT_LOAD_ERROR.ON_ERROR, {
      component: 'RiveErrorBoundary',
      error: error.toString(),
      componentStack: errorInfo?.componentStack,
      errorBoundary: true,
    });
  }

  resetError = (): void => {
    this.setState({
      hasError: false,
      error: null,
    });
  };

  render(): ReactNode {
    const { hasError } = this.state;
    const { children, fallbackText = 'Animation unavailable', style } = this.props;

    if (hasError) {
      return (
        <View style={[style, { justifyContent: 'center', alignItems: 'center' }]}>
          <Text style={{
            color: dark.colors.textDark,
            textAlign: 'center',
            fontSize: 12,
            opacity: 0.7,
          }}>
            {fallbackText}
          </Text>
          <Text 
            style={{
              color: dark.colors.primary,
              textAlign: 'center',
              fontSize: 10,
              marginTop: 4,
              textDecorationLine: 'underline',
            }}
            onPress={this.resetError}
          >
            Tap to retry
          </Text>
        </View>
      );
    }

    return children;
  }
}

export const withRiveErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallbackText?: string
) => {
  const RiveErrorBoundaryWrapper = (props: P & { style?: any }) => (
    <RiveErrorBoundary fallbackText={fallbackText} style={props.style}>
      <Component {...props} />
    </RiveErrorBoundary>
  );

  RiveErrorBoundaryWrapper.displayName = `withRiveErrorBoundary(${Component.displayName || Component.name})`;

  return React.memo(RiveErrorBoundaryWrapper);
};

export default RiveErrorBoundary;
