import React, { useEffect, useState } from 'react';
import Rive from 'rive-react-native';
import { Text, View, ViewStyle } from 'react-native';
import dark from 'core/constants/themes/dark';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';

interface RiveComponentProps {
  url: string;
  resourceName?: string;
  artboardName?: string;
  stateMachineName?: string;
  style?: ViewStyle;
  autoPlay?: boolean;
  loop?: boolean;
  onLoopEnd?: () => void;
  fallbackText?: string;
  enableRetry?: boolean;
  maxRetries?: number;
  timeout?: number;
}

const RiveComponent: React.FC<RiveComponentProps> = (props) => {
  const {
    url,
    resourceName,
    artboardName,
    stateMachineName,
    style,
    autoPlay = true,
    loop = true,
    onLoopEnd,
    fallbackText = 'Animation unavailable',
    // New props are accepted but not used in minimal version
    enableRetry,
    maxRetries,
    timeout,
  } = props;

  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    setHasError(false);
  }, [url]);

  const handleError = (error: any) => {
    console.warn('Rive animation error:', error);
    setHasError(true);
    
    // Track error with analytics
    Analytics.track(ANALYTICS_EVENTS.COMPONENT_LOAD_ERROR.ON_ERROR, {
      component: 'RiveComponent',
      error: error?.message || error?.toString() || 'Unknown Rive error',
      url,
    });
  };

  if (hasError) {
    return (
      <View style={[style, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{ 
          color: dark.colors.textDark, 
          textAlign: 'center',
          fontSize: 12,
          opacity: 0.7,
        }}>
          {fallbackText}
        </Text>
      </View>
    );
  }

  // Render original Rive component with minimal error handling
  return (
    <Rive
      url={url}
      resourceName={resourceName}
      stateMachineName={stateMachineName}
      artboardName={artboardName}
      style={style}
      autoplay={autoPlay}
      loop={loop}
      onLoopEnd={onLoopEnd}
      onError={handleError}
    />
  );
};

export default React.memo(RiveComponent);
