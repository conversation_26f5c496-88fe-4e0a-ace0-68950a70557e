import Rive, { Alignment, Fit, Layout } from '@rive-app/react-canvas';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { View, Text } from 'react-native';
import dark from 'core/constants/themes/dark';

const RiveComponent = (props) => {
  const {
    url,
    artboardName,
    stateMachineName,
    style,
    autoPlay,
    fallbackText = 'Animation unavailable',
    enableRetry = false,
    maxRetries = 0,
    timeout = 10000,
    onLoopEnd,
    ...restProps
  } = props;

  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  if (hasError) {
    return (
      <View style={[style, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{
          color: dark.colors.textDark,
          textAlign: 'center',
          fontSize: 12,
          opacity: 0.7,
        }}>
          {fallbackText}
        </Text>
      </View>
    );
  }

  if (isLoading) {
    return (
      <View style={[style, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{
          color: dark.colors.textDark,
          textAlign: 'center',
          fontSize: 10,
          opacity: 0.5,
        }}>
          Loading animation...
        </Text>
      </View>
    );
  }

  return (
    <Rive
      src={url}
      stateMachines={stateMachineName}
      style={style}
      layout={new Layout({ fit: Fit.Contain, alignment: Alignment.Center })}
      onLoad={handleLoad}
      onLoadError={handleError}
      onLoopEnd={onLoopEnd}
      {...restProps}
    />
  );
};

RiveComponent.propTypes = {
  url: PropTypes.string.isRequired,
  loop: PropTypes.bool,
  artboardName: PropTypes.string,
  stateMachineName: PropTypes.string,
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  fallbackText: PropTypes.string,
  enableRetry: PropTypes.bool,
  maxRetries: PropTypes.number,
  timeout: PropTypes.number,
  onLoopEnd: PropTypes.func,
};

export default React.memo(RiveComponent);
