import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: dark.colors.gradientBackground,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 16,
    color: dark.colors.errorDark,
    fontFamily: 'Montserrat-500',
    maxWidth: 300,
    textAlign: 'center',
    marginBottom: 10,
    letterSpacing: 0.3,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  actionsContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    marginTop: 20,
    gap: 16,
  },
  actionButton: {
    paddingVertical: 14,
    paddingHorizontal: 28,
    borderRadius: 14,
    width: 140,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5,
  },
  primaryActionButton: {
    backgroundColor: dark.colors.primary,
    borderWidth: 0,
  },
  primaryActionButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
    fontFamily: 'Montserrat-600',
    letterSpacing: 0.5,
  },
  secondaryActionButton: {
    backgroundColor: dark.colors.cardBackground,
    borderWidth: 1,
    borderColor: dark.colors.secondary,
  },
  secondaryActionButtonText: {
    color: dark.colors.secondary,
    fontWeight: '500',
    fontSize: 10,
    fontFamily: 'Montserrat-600',
    letterSpacing: 0.5,
  },

  emptyStateContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    width: '100%',
    height: '100%',
  },
  pookieImage: {
    width: 100,
    height: 'auto',
    aspectRatio: 96 / 78,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
});

export default styles;
