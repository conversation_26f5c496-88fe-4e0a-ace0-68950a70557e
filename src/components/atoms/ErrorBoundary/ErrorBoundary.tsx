import React, { Component, ErrorInfo, ReactNode } from 'react';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import DefaultErrorView from 'atoms/ErrorBoundary/DefaultErrorView';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  componentName: string;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    Analytics.track(ANALYTICS_EVENTS.COMPONENT_LOAD_ERROR.ON_ERROR, {
      error: error.toString(),
      componentStack: errorInfo?.componentStack,
      componentName: this.props.componentName || 'Unknown',
    });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  resetError = (): void => {
    this.setState({
      hasError: false,
      error: null,
    });
  };

  render(): ReactNode {
    const { hasError, error } = this.state;
    const { children, fallback } = this.props;

    if (hasError) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback;
      }

      // Default error UI
      return <DefaultErrorView />;
    }

    return children;
  }
}

export const WithErrorBoundary = (Component) => {
  const ErrorBoundaryWrapper = (props) => (
    <ErrorBoundary componentName={Component.displayName ?? Component.name}>
      <Component {...props} />
    </ErrorBoundary>
  );

  ErrorBoundaryWrapper.displayName = `WithErrorBoundary(${Component.displayName || ''})`;

  return React.memo(ErrorBoundaryWrapper);
};

export default ErrorBoundary;
