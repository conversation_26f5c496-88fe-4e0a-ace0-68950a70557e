import React, { useCallback, useMemo } from 'react';
import LinearGradient from 'atoms/LinearGradient';
import * as Animatable from 'react-native-animatable';
import { withOpacity } from 'core/utils/colorUtils';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import {
  ActivityIndicator,
  Button,
  GestureResponderEvent,
  PanResponder,
  PanResponderGestureState,
  PanResponderInstance,
  Platform,
  StyleSheet,
  Text,
  View,
} from 'react-native';

import IconButton from 'atoms/IconButton';
import styles, { styleVariables } from './Toast.style';
import { ToastPropDetails, ToastProps, ToastState, ToastType } from './types';

const ICON_SIZE_WRAPPED = 30;

const styleVariablesWrapped = {
  dismissColor: '#A0A0A8',
  loaderColor: '#6AC96E',
};

const stylesWrapped = StyleSheet.create({
  textStyle: {
    fontSize: 14,
    fontFamily: 'Montserrat-600',
    color: '#fff',
  },
  iconContainerStyle: {
    width: 30,
    height: 30,
  },
  contentContainerStyle: {
    borderRadius: 8,
  },
  iconStyle: {
    ...Platform.select({
      android: {
        lineHeight: 30,
        height: 30,
      },
    }),
  },
  // dismiss Icon
  dismissIcon: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
});

type ToastInstance = InstanceType<typeof Toast>;

export const TOAST_TYPE = {
  LOADING: 'Loading',
  PROGRESS: 'Progress',
  SUCCESS: 'Success',
  ERROR: 'Error',
  INFO: 'Info',
} as const;

const ICON_SIZE = 30;
const LOADER_SIZE = 'large';
const TIME_INTERVAL = 3500;

const DEFAULT_IN_ANIMATION = 'fadeInUp';
const DEFAULT_OUT_ANIMATION = 'fadeOutDown';
const DEFAULT_IN_ANIMATION_DURATION = 300;
const DEFAULT_OUT_ANIMATION_DURATION = 270;
const Y_AXIS_VALUE_TO_DISMISS = 10;

const DEFAULT_LOADING_TEXT = 'Updating...';

const TOAST_PROPS: { [key in ToastType]?: ToastPropDetails } = {
  [TOAST_TYPE.SUCCESS]: {
    iconProps: { name: 'check-circle', color: '#6AC96E', size: 24 },
    backgroundColor: styleVariables.toastBackgroundColor,
  },
  [TOAST_TYPE.ERROR]: {
    iconProps: { name: 'error-outline', color: '#ff6060' },
    backgroundColor: styleVariables.toastBackgroundColor,
  },
  [TOAST_TYPE.INFO]: {
    iconProps: { name: 'info-outline', color: '#F3BF24' },
    backgroundColor: styleVariables.toastBackgroundColor,
  },
};

const LINEAR_GRADIENT_CONFIG = {
  start: { x: 0, y: 1 },
  end: { x: 1, y: 0 },
  locations: [0.0, 1],
};

let ToastDefaultProps: { [key in ToastType]?: Partial<ToastPropDetails> } = {};

let instance: Toast | null = null;

class Toast extends React.PureComponent<ToastProps, ToastState> {
  private panResponder: PanResponderInstance;

  private container: (Animatable.View & View) | null = null;

  private toastTimeout: NodeJS.Timeout | null = null;

  constructor(props: ToastProps) {
    super(props);
    this.state = {
      show: false,
    };
    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponderCapture: (evt, gestureState) =>
        gestureState.dx !== 0 && gestureState.dy !== 0,
      onMoveShouldSetPanResponderCapture: (evt, gestureState) =>
        gestureState.dx !== 0 && gestureState.dy !== 0,
      onPanResponderMove: this.handlePanResponderMove,
    });

    instance = this;
  }

  static getInstance(): Toast | null {
    return instance;
  }

  componentDidUpdate(
    prevProps: Readonly<ToastProps>,
    prevState: Readonly<ToastState>,
  ) {
    if (this.container && this.state.show && !prevState.show) {
      if (
        this.state.type !== TOAST_TYPE.LOADING &&
        this.state.type !== TOAST_TYPE.PROGRESS
      ) {
        this.container.animate(
          DEFAULT_IN_ANIMATION,
          DEFAULT_IN_ANIMATION_DURATION,
        );
      }
    }

    if (
      this.state.show &&
      (this.state.show !== prevState.show ||
        this.state.type !== prevState.type ||
        this.state.timeInterval !== prevState.timeInterval)
    ) {
      this.setResetTimer({
        type: this.state.type,
        timeInterval: this.state.timeInterval,
      });
    }
  }

  componentWillUnmount() {
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
    }
    instance = null;
  }

  render() {
    if (!this.state.show) {
      return null;
    }

    return (
      <View
        style={[styles.container, this.props.toastContainerStyle]}
        pointerEvents="box-none"
      >
        {this.renderToast()}
      </View>
    );
  }

  renderToast = (): React.ReactNode => {
    const { type } = this.state;
    switch (type) {
      case TOAST_TYPE.LOADING:
      case TOAST_TYPE.PROGRESS:
        return this.renderLoadingToast();
      case TOAST_TYPE.SUCCESS:
      case TOAST_TYPE.ERROR:
      case TOAST_TYPE.INFO:
        return this.renderStatusToast();
      default: {
        return null;
      }
    }
  };

  renderLoadingToast = (): React.ReactNode => {
    const toastBackgroundColor =
      this.getToastProps(this.state.type)?.backgroundColor ??
      styleVariables.toastBackgroundColor;
    const renderDismissIcon =
      this.state.renderDismissIcon ?? this.props.renderDismissIcon;
    const { loadingToastContainerStyle } = this.props;

    return (
      <View
        style={[
          styles.loadingContentContainer,
          { shadowColor: toastBackgroundColor },
        ]}
      >
        <LinearGradient
          {...LINEAR_GRADIENT_CONFIG}
          colors={[
            toastBackgroundColor,
            withOpacity(toastBackgroundColor, 0.8),
          ]}
          style={[styles.loadingToastContainer, loadingToastContainerStyle]}
        >
          {this.renderLoader()}
          {this.renderLoadingText()}
          {renderDismissIcon ? renderDismissIcon() : null}
        </LinearGradient>
      </View>
    );
  };

  renderLoader = (): React.ReactNode => {
    const loaderProps = this.state.loaderProps ?? this.props.loaderProps;
    return (
      <ActivityIndicator
        size={LOADER_SIZE}
        // backgroundStyle={styles.loaderContainer}
        style={styles.loaderContainer}
        color={styleVariables.loaderColor}
        {...loaderProps}
      />
    );
  };

  renderLoadingText = (): React.ReactNode => {
    const loadingTextStyle = this.props.loadingTextStyle
      ? [styles.loadingText, this.props.loadingTextStyle]
      : styles.loadingText;

    const { title, description } = this.state;
    if (_isEmpty(title) && _isEmpty(description)) {
      return <Text style={loadingTextStyle}>{DEFAULT_LOADING_TEXT}</Text>;
    }

    return (
      <>
        {title ? <Text style={loadingTextStyle}>{title}</Text> : null}
        {description ? (
          <Text style={loadingTextStyle}>{description}</Text>
        ) : null}
      </>
    );
  };

  renderStatusToast = (): React.ReactNode => {
    const { contentContainerStyle } = this.props;
    const { renderStatusContent, getToastBackgroundColor, type } = this.state;
    const toastBackgroundColor =
      getToastBackgroundColor?.(type!) ??
      this.getToastProps(type)?.backgroundColor ??
      styleVariables.toastBackgroundColor;
    return (
      <Animatable.View
        ref={this.setRef}
        useNativeDriver
        style={[
          styles.contentContainer,
          { shadowColor: toastBackgroundColor },
          contentContainerStyle,
        ]}
        {...this.panResponder.panHandlers}
      >
        {renderStatusContent
          ? renderStatusContent(this.state)
          : this.renderStatusToastContent()}
      </Animatable.View>
    );
  };

  renderStatusToastContent = (): React.ReactNode => {
    const toastBackgroundColor =
      this.getToastProps(this.state.type)?.backgroundColor ??
      styleVariables.toastBackgroundColor;
    const renderDismissIcon =
      this.state.renderDismissIcon ?? this.props.renderDismissIcon;
    return (
      <LinearGradient
        {...LINEAR_GRADIENT_CONFIG}
        colors={[toastBackgroundColor, withOpacity(toastBackgroundColor, 0.8)]}
        style={[styles.toastGradientContainer, this.props.containerStyle]}
      >
        <View style={styles.toastContainer}>
          {this.renderAsset()}
          {this.renderToastInfo({
            title: this.state.title,
            description: this.state.description,
          })}
        </View>
        {this.renderCTA()}
        {renderDismissIcon ? renderDismissIcon() : null}
      </LinearGradient>
    );
  };

  renderAsset = (): React.ReactNode => {
    const { iconContainerStyle } = this.props;
    const iconProps =
      this.state.iconProps ??
      this.props.iconProps ??
      this.getToastProps(this.state.type)?.iconProps;
    const { name, color, size, iconStyle } = iconProps || {};

    if (!name) return null;

    return (
      <View style={[styles.iconStyle, iconContainerStyle]}>
        <MaterialIcons
          name={name as any}
          color={color || styleVariables.iconColor}
          size={size || ICON_SIZE}
          style={iconStyle}
        />
      </View>
    );
  };

  renderToastInfo = ({
    title,
    description,
  }: {
    title?: string;
    description?: string;
  }): React.ReactNode => (
    <View style={[styles.messageContainer, this.props.messageContainerStyle]}>
      {title ? (
        <Text style={[styles.title, this.props.titleStyle]} numberOfLines={1}>
          {title}
        </Text>
      ) : null}
      {description ? (
        <Text
          // id={description}
          style={[styles.description, this.props.descriptionStyle]}
          numberOfLines={3}
        >
          {description}
        </Text>
      ) : null}
    </View>
  );

  renderCTA = (): React.ReactNode => {
    const {
      cta: { label = 'Learn more', onPress = _noop, buttonData } = {},
      showCTA = false,
    } = this.state;
    if (!showCTA) {
      return null;
    }
    return (
      <View style={styles.ctaViewContainer}>
        <Button
          onPress={onPress}
          // data={buttonData}
          // hasTouchFeedback={false}
          title={label}
        />
        {/* <Button
            onPress={onPress}
            data={buttonData}
            hasTouchFeedback={false}
        >
            <View style={styles.ctaButtonContainer}>
                <Text style={styles.ctaText}>{label}</Text>
            </View>
        </Button> */}
      </View>
    );
  };

  showToast = (props: Partial<Omit<ToastState, 'show'>>) => {
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
      this.toastTimeout = null;
    }
    this.setState({
      ...props,
      show: true,
      timeInterval: props.timeInterval ?? TIME_INTERVAL,
      showCTA: props.showCTA ?? false,
      cta: props.cta ?? undefined,
    });
  };

  resetToast = () => {
    this.setState({
      show: false,
      type: undefined,
      timeInterval: TIME_INTERVAL,
      showCTA: false,
      cta: undefined,
    });
  };

  setResetTimer = ({
    type,
    timeInterval = TIME_INTERVAL,
  }: { type?: ToastType; timeInterval?: number } = {}) => {
    if (type === TOAST_TYPE.LOADING || type === TOAST_TYPE.PROGRESS) {
      return;
    }

    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
      this.toastTimeout = null;
    }

    this.toastTimeout = setTimeout(() => {
      this.toastTimeout = null;

      if (!this.container) {
        this.resetToast();
        return;
      }

      this.container
        .animate(DEFAULT_OUT_ANIMATION, DEFAULT_OUT_ANIMATION_DURATION)
        .then(() => {
          if (this.state.show) {
            this.resetToast();
          }
        })
        .catch((error) => {
          if (this.state.show) {
            this.resetToast();
          }
        });
    }, timeInterval);
  };

  getToastProps = (type?: ToastType): Partial<ToastPropDetails> | undefined => {
    const mergedToastProps = {
      ...TOAST_PROPS,
      ...ToastDefaultProps,
    };

    return type
      ? mergedToastProps[type] || mergedToastProps[TOAST_TYPE.INFO]
      : undefined;
  };

  handlePanResponderMove = (
    event: GestureResponderEvent,
    gestureState: PanResponderGestureState,
  ) => {
    if (gestureState.dy < -Y_AXIS_VALUE_TO_DISMISS) {
      this.setResetTimer({ type: this.state.type, timeInterval: 0 });
    }
  };

  setRef = (c: (Animatable.View & View) | null) => {
    this.container = c;
  };
}

export function showToast(props: Partial<Omit<ToastState, 'show'>> = {}) {
  const toastInstance = Toast.getInstance();
  if (toastInstance) {
    toastInstance.showToast(props);
  }
}

export function hideToast() {
  const toastInstance = Toast.getInstance();
  if (toastInstance) {
    toastInstance.resetToast();
  }
}

export function setToastDefaultProps(
  toastDefaultProps: typeof ToastDefaultProps,
) {
  ToastDefaultProps = toastDefaultProps;
}

const WrappedToastComponent = React.forwardRef<
  ToastInstance,
  Omit<React.ComponentProps<typeof Toast>, 'ref'>
>(({ ...restProps }, ref) => {
  const renderDismissIcon = useCallback(
    () => (
      <IconButton
        name="close"
        color={styleVariablesWrapped.dismissColor}
        size={20}
        onPress={hideToast}
        buttonStyle={stylesWrapped.dismissIcon}
        // hoverable={false}
      />
    ),
    [],
  );

  const loaderProps = useMemo(
    () => ({
      color: styleVariablesWrapped.loaderColor,
      size: ICON_SIZE_WRAPPED,
    }),
    [],
  );

  return (
    <Toast
      {...restProps}
      ref={ref}
      titleStyle={stylesWrapped.textStyle}
      descriptionStyle={stylesWrapped.textStyle}
      loadingTextStyle={stylesWrapped.textStyle}
      iconContainerStyle={stylesWrapped.iconContainerStyle}
      contentContainerStyle={stylesWrapped.contentContainerStyle}
      loaderProps={loaderProps}
      renderDismissIcon={renderDismissIcon}
    />
  );
});

export default React.memo(WrappedToastComponent);
