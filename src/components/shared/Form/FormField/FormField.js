import React from 'react';

import { FORM_INPUT_TYPES } from 'core/constants/forms';
import PropTypes from 'prop-types';
import _values from 'lodash/values';
import _isEmpty from 'lodash/isEmpty';
import Checkbox from '../components/Checkbox';
import TextInput from '../components/TextInput';
import Multiselect from '../components/Multiselect';
import NumberInput from '../components/NumberInput';
import SingleSelect from '../components/SingleSelect';

import SingleSelectOption from '../components/SingleSelectOption';
import DropdownComponentWithLabel from '../components/DropdownComponentWithLabel';
import ToggleComponent from '../components/ToggleComponent';
import DateTimePickerInput from '../components/DateTimePickerInput';
import RangeSelector from '../components/RangeSelector';

const FormField = (props) => {
  const { field, value, onValueChange, error } = props;

  if (_isEmpty(field)) {
    return null;
  }

  switch (field.type) {
    case FORM_INPUT_TYPES.CHECKBOX:
      return <Checkbox {...props} />;
    case FORM_INPUT_TYPES.NUMBER_INPUT:
      return <NumberInput {...props} />;
    case FORM_INPUT_TYPES.INLINE_TEXT_INPUT:
    case FORM_INPUT_TYPES.MULTILINE_TEXT_INPUT:
    case FORM_INPUT_TYPES.TEXT:
      return <TextInput field={field} {...props} />;
    case FORM_INPUT_TYPES.TEXTAREA:
      return <TextInput field={field} {...props} />;
    case FORM_INPUT_TYPES.SINGLE_SELECT:
      return <SingleSelect {...props} />;
    case FORM_INPUT_TYPES.MULTISELECT:
      return <Multiselect {...props} />;
    case FORM_INPUT_TYPES.SINGLE_SELECT_OPTION:
      return (
        <SingleSelectOption
          {...props}
          onSelect={onValueChange}
          selectedOption={value}
        />
      );
    case FORM_INPUT_TYPES.RANGE_SELECTOR:
      return <RangeSelector {...props} onChange={onValueChange} />;
    case FORM_INPUT_TYPES.DROPDOWN:
      return <DropdownComponentWithLabel {...props} onChange={onValueChange} />;
    case FORM_INPUT_TYPES.TOGGLE_BUTTON:
      return <ToggleComponent {...props} onValueChange={onValueChange} />;
    case FORM_INPUT_TYPES.DATE_TIME_PICKER:
      return <DateTimePickerInput {...props} onValueChange={onValueChange} />;
    default:
      return null;
  }
};

FormField.propTypes = {
  field: PropTypes.shape({
    type: PropTypes.oneOf(_values(FORM_INPUT_TYPES)),
    label: PropTypes.string,
    options: PropTypes.arrayOf(PropTypes.string),
    name: PropTypes.string.isRequired, // Added name prop
    style: PropTypes.object, // Added style prop
  }).isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  onValueChange: PropTypes.func.isRequired,
  error: PropTypes.string,
};

export default React.memo(FormField);
