import React, { useCallback, useState } from 'react';
import { View, TextInput, TouchableOpacity, Text } from 'react-native';
import _toString from 'lodash/toString';
import _toNumber from 'lodash/toNumber';
import _isNil from 'lodash/isNil';
import FormFieldLabel from '../FormFieldLabel';
import FormFieldError from '../FormFieldError';
import styles from './RangeSelector.style';
import { InputType, NumInputProps, RangeSelectorProps } from './types';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';

const RangeSelector: React.FC<RangeSelectorProps> = (props) => {
  const { field, error, onChange } = props;
  const { rules, defaultValue } = field;
  const { minValue, maxValue } = rules;
  const { isMobile: isCompact } = useMediaQuery();

  const [currentValue, setCurrentValue] = useState({
    min: Math.max(
      minValue,
      Math.min(defaultValue.min, defaultValue.max ?? maxValue),
    ),
    max: Math.min(
      maxValue,
      Math.max(defaultValue.max ?? minValue, defaultValue.min),
    ),
  });

  const [minInputText, setMinInputText] = useState(_toString(currentValue.min));
  const [maxInputText, setMaxInputText] = useState(_toString(currentValue.max));

  const handleMinInputChange = useCallback((text: string) => {
    setMinInputText(text);
  }, []);

  const handleMaxInputChange = useCallback((text: string) => {
    setMaxInputText(text);
  }, []);

  const handleMinInputBlur = useCallback(() => {
    const parsedValue = _toNumber(minInputText);
    if (_isNil(parsedValue) || isNaN(parsedValue)) {
      setMinInputText(_toString(currentValue.min));
      return;
    }

    let newMin: number;

    newMin = Math.max(minValue, Math.min(parsedValue, currentValue.max));

    setCurrentValue({ ...currentValue, min: newMin });
    setMinInputText(_toString(newMin));
    onChange({ ...currentValue, min: newMin });
  }, [minInputText, minValue, currentValue, onChange]);

  const handleMaxInputBlur = useCallback(() => {
    const parsedValue = _toNumber(maxInputText);
    if (_isNil(parsedValue) || isNaN(parsedValue)) {
      setMaxInputText(_toString(currentValue.max));
      return;
    }
    let newMax: number;

    newMax = Math.min(maxValue, Math.max(parsedValue, currentValue.min));

    setCurrentValue({ ...currentValue, max: newMax });
    setMaxInputText(_toString(newMax));
    onChange({ ...currentValue, max: newMax });
  }, [maxInputText, maxValue, currentValue, onChange]);

  const handleIncrease = useCallback(
    (type: InputType) => {
      if (type === InputType.MIN) {
        const currentMin = _toNumber(minInputText);
        if (!_isNil(currentMin) && !isNaN(currentMin)) {
          const newMin = Math.min(currentMin + 1, currentValue.max, maxValue);
          setCurrentValue((prev) => ({ ...prev, min: newMin }));
          setMinInputText(_toString(newMin));
          onChange({ ...currentValue, min: newMin });
        } else {
          setMinInputText(_toString(currentValue.min));
        }
      } else {
        const currentMax = _toNumber(maxInputText);
        if (!_isNil(currentMax) && !isNaN(currentMax)) {
          const newMax = Math.min(currentMax + 1, maxValue);
          setCurrentValue((prev) => ({ ...prev, max: newMax }));
          setMaxInputText(_toString(newMax));
          onChange({ ...currentValue, max: newMax });
        } else {
          setMaxInputText(_toString(currentValue.max));
        }
      }
    },
    [minInputText, maxInputText, currentValue, maxValue, onChange],
  );

  const handleDecrease = useCallback(
    (type: InputType) => {
      if (type === InputType.MIN) {
        const currentMin = _toNumber(minInputText);
        if (!_isNil(currentMin) && !isNaN(currentMin)) {
          const newMin = Math.max(currentMin - 1, minValue);
          setCurrentValue((prev) => ({ ...prev, min: newMin }));
          setMinInputText(_toString(newMin));
          onChange({ ...currentValue, min: newMin });
        } else {
          setMinInputText(_toString(currentValue.min));
        }
      } else {
        const currentMax = _toNumber(maxInputText);
        if (!_isNil(currentMax) && !isNaN(currentMax)) {
          const newMax = Math.max(currentMax - 1, currentValue.min, minValue);
          setCurrentValue((prev) => ({ ...prev, max: newMax }));
          setMaxInputText(_toString(newMax));
          onChange({ ...currentValue, max: newMax });
        } else {
          setMaxInputText(_toString(currentValue.max));
        }
      }
    },
    [minInputText, maxInputText, currentValue, minValue, onChange],
  );

  return (
    <View
      style={[
        styles.container,
        isCompact && {
          flexDirection: 'column',
          gap: 8,
          alignItems: 'flex-start',
        },
      ]}
    >
      <FormFieldLabel field={field} />
      <View
        style={[
          { flexDirection: 'row', gap: 8 },
          isCompact && { justifyContent: 'center', width: '100%' },
        ]}
      >
        <NumInput
          type={InputType.MIN}
          inputLabel="Min"
          inputText={minInputText}
          handleInputBlur={handleMinInputBlur}
          handleInputChange={handleMinInputChange}
          handleDecrease={handleDecrease}
          handleIncrease={handleIncrease}
        />
        <NumInput
          type={InputType.MAX}
          inputLabel="Max"
          inputText={maxInputText}
          handleInputBlur={handleMaxInputBlur}
          handleInputChange={handleMaxInputChange}
          handleIncrease={handleIncrease}
          handleDecrease={handleDecrease}
        />
      </View>
      <FormFieldError error={error} />
    </View>
  );
};

export default React.memo(RangeSelector);

const NumInput: React.FC<NumInputProps> = ({
  type,
  inputText,
  inputLabel,
  handleInputChange,
  handleInputBlur,
  handleIncrease,
  handleDecrease,
}) => {
  return (
    <View style={{ alignItems: 'center' }}>
      <Text style={styles.inputLabel}>{inputLabel}</Text>
      <View style={styles.inputContainer}>
        <TouchableOpacity
          style={styles.subButton}
          onPress={() => handleDecrease(type)}
        >
          <Text style={styles.subText}>-</Text>
        </TouchableOpacity>
        <TextInput
          defaultValue={inputText}
          style={styles.value}
          value={inputText}
          onChangeText={handleInputChange}
          onBlur={handleInputBlur}
          keyboardType="number-pad"
        />
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => handleIncrease(type)}
        >
          <Text style={styles.addText}>+</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};
