import useUserStore from 'store/useUserStore';
import { useRef, useEffect } from 'react';
import { drawQuestion } from '@/src/wasm';
import { View } from 'react-native';

const SecureCanvas = ({
  width = 400,
  height = 400,
  questionId = '',
  renderQuestionOverlay,
}: any) => {
  const wrapperRef = useRef(null);
  const canvasRef = useRef(document.createElement('canvas'));
  const { isWasmReady } = useUserStore((state) => ({
    isWasmReady: state.isWasmReady,
  }));

  const isAlreadyShadowed = useRef(false);

  useEffect(() => {
    if (!isWasmReady) return;
    const canvas = canvasRef.current;
    canvas.width = width * 2;
    canvas.height = height * 2;

    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;

    const shadowHost = wrapperRef.current;
    if (!shadowHost) return;
    if (!isAlreadyShadowed.current) {
      const shadow = (shadowHost as HTMLDivElement)?.attachShadow({
        mode: 'closed',
      });
      shadow?.appendChild(canvas);
      isAlreadyShadowed.current = true;
    }

    const ctx = canvas.getContext('2d');
    const render = () => {
      if (!ctx || !questionId) return;
      drawQuestion(questionId, canvasRef.current, ctx);
    };

    render();

    const fakeBlob = new Blob(['Access Denied'], { type: 'text/plain' });
    canvas.toBlob = (cb) => {
      cb(fakeBlob);
    };

    canvas.toDataURL = () => 'data:image/png;base64,';
  }, [width, height, isWasmReady, questionId]);

  return (
    <View
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
      }}
    >
      <div
        ref={wrapperRef}
        style={{
          width,
          height,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          overflow: 'hidden',
          userSelect: 'none',
          pointerEvents: 'none',
        }}
      />
      {renderQuestionOverlay()}
    </View>
  );
};

export default SecureCanvas;
