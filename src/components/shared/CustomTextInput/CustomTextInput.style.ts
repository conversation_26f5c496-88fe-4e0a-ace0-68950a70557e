import { StyleSheet } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  networkErrorOverlayContainer: {
    position: 'absolute',
    left: -50,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputContainer: {
    width: '60%',
    marginBottom: 16,
    paddingVertical: 0,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Dark.colors.tertiary,
    borderRadius: 12,
    borderWidth: 1,
    textAlign: 'center',
    borderColor: '#000',
  },
  input: {
    width: '100%',
    fontSize: 20,
    color: 'white',
    fontFamily: 'Montserrat-400',
    outlineStyle: 'none',
    borderWidth: 0,
    paddingHorizontal: 8,
    paddingVertical: 10,
    textAlign: 'center',
  },
  incorrectContainer: {
    position: 'absolute',
    right: 10,
    top: '60%',
    paddingHorizontal: 4,
    transform: [{ translateY: -20 }],
    borderRadius: 4,
    borderWidth: 0.5,
    borderColor: 'red',
    backgroundColor: Dark.colors.incorrectBackground,
  },
  correctContainer: {
    position: 'absolute',
    right: 10,
    top: '60%',
    paddingHorizontal: 4,
    transform: [{ translateY: -20 }],
    borderRadius: 4,
    borderWidth: 0.5,
    borderColor: Dark.colors.secondary,
    backgroundColor: Dark.colors.secondaryLight,
  },
  iconWrapper: {
    padding: 5,
  },
  title: {
    marginBottom: 5,
    fontFamily: 'Montserrat-700',
    fontSize: 8,
    color: Dark.colors.textLight,
    opacity: 0.4,
  },
});

export default styles;
