import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { Pressable, Text, View } from 'react-native';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import Dark from '@/src/core/constants/themes/dark';
import dark from '@/src/core/constants/themes/dark';
import { SOLUTION_STATUS } from '@/src/modules/game/pages/PlayGame/Footer/AnswerEvaluators';
import NetworkErrorOverlay from '@/src/components/shared/NetworkErrorOverlay';
import _omit from 'lodash/omit';
import { SolutionStatus } from '@/src/modules/game/pages/PlayGame/Footer/types';
import SecuredTextInput from 'atoms/SecuredTextInput';
import CustomKeyboard, { KEYBOARD_TYPES } from '../CustomKeyboard';
import styles from './CustomTextInput.style';
import { KeyboardType } from '../CustomKeyboard/types';

interface CustomTextInputProps {
  value?: string;
  onChangeText: (text: string) => void;
  showNetworkError?: boolean;
  solutionStatus?: SolutionStatus;
  customKeyboardType?: KeyboardType;
  onClearAll?: () => void;
  customKeyboard?: boolean;
}

const CustomTextInput: React.FC<CustomTextInputProps> = ({
  value = '',
  onChangeText,
  showNetworkError = true,
  solutionStatus = SOLUTION_STATUS.NOT_ANSWERED,
  customKeyboardType = KEYBOARD_TYPES.NUMBERS,
  onClearAll,
  customKeyboard = true,
  ...props
} = EMPTY_OBJECT) => {
  const valueRef = useRef(value);

  useEffect(() => {
    valueRef.current = value;
  }, [value]);

  const vibrateFeedback = useCallback(() => {
    if (typeof navigator !== 'undefined' && navigator.vibrate) {
      navigator.vibrate(10);
    }
  }, []);

  const handleKeyPress = useCallback(
    (key: string) => {
      // vibrateFeedback();
      if (key === 'clr') {
        valueRef.current = '';
        onChangeText('');
      } else if (key === 'space') {
        valueRef.current += ' ';
        onChangeText(valueRef.current);
      } else {
        valueRef.current += key;
        onChangeText(valueRef.current);
      }
    },
    [onChangeText],
  );

  const handleDelete = useCallback(() => {
    // vibrateFeedback()
    valueRef.current = valueRef.current.slice(0, -1);
    onChangeText(valueRef.current);
  }, [onChangeText]);

  const handleClear = useCallback(() => {
    valueRef.current = '';
    onChangeText('');
  }, [onChangeText]);

  const handleDone = () => {
    // setKeyboardVisible(false);
  };

  const textInputProps = useMemo(
    () => ({
      value,
      onChangeText,
      placeholderTextColor: dark.colors.inputPlaceholder,
      ..._omit(props, ['showNetworkError', 'solutionStatus', 'customKeyboard']),
      style: styles.input,
      editable: !customKeyboard,
    }),
    [value, onChangeText, props, customKeyboard],
  );

  const keyboardProps = useMemo(
    () => ({
      onKeyPress: handleKeyPress,
      onDelete: handleDelete,
      customKeyboardType,
    }),
    [handleKeyPress, handleDelete, customKeyboardType],
  );

  const statusContainer = useMemo(() => {
    if (solutionStatus === SOLUTION_STATUS.INCORRECT) {
      return (
        <View style={styles.incorrectContainer}>
          <Pressable
            onPress={onClearAll || handleClear}
            style={styles.iconWrapper}
          >
            <FontAwesome name="close" size={20} color={Dark.colors.errorDark} />
          </Pressable>
        </View>
      );
    }
    if (solutionStatus === SOLUTION_STATUS.CORRECT) {
      return (
        <View style={styles.correctContainer}>
          <View style={styles.iconWrapper}>
            <FontAwesome name="check" size={20} color={Dark.colors.success} />
          </View>
        </View>
      );
    }
    return null;
  }, [solutionStatus, onClearAll, handleClear]);

  const networkErrorOverlay = useMemo(() => {
    if (!showNetworkError) return null;
    return (
      <View style={styles.networkErrorOverlayContainer}>
        <NetworkErrorOverlay />
      </View>
    );
  }, [showNetworkError]);

  const titleText = useMemo(
    () => <Text style={styles.title}>TYPE OUT YOUR ANSWER</Text>,
    [],
  );

  const keyboardComponent = useMemo(() => {
    if (!customKeyboard) return null;
    return <CustomKeyboard {...keyboardProps} />;
  }, [customKeyboard, keyboardProps]);

  return (
    <View style={styles.container}>
      {titleText}
      <View style={styles.inputContainer}>
        {networkErrorOverlay}
        <SecuredTextInput {...textInputProps} />
        {statusContainer}
      </View>
      {keyboardComponent}
    </View>
  );
};

export default React.memo(CustomTextInput);
