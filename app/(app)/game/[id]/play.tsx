import React, { useEffect } from 'react';
import JoinG<PERSON> from 'modules/game/pages/JoinGame';
import WithMatiksBackground from 'atoms/WithMatiksBackground';
import { INITIAL_ROUTE_KEY } from 'core/constants/appConstants';
import { Redirect, useLocalSearchParams } from 'expo-router';
import _isEmpty from 'lodash/isEmpty';
import { setStorageItemAsync } from 'core/hooks/useStorageState';

const WaitingForFriendContainer = () => {
  const searchParams = useLocalSearchParams();
  const { id: gameId } = searchParams;

  useEffect(() => {
    setStorageItemAsync(INITIAL_ROUTE_KEY, null);
  }, []);

  if (_isEmpty(gameId)) {
    return <Redirect href="/home" />;
  }

  return (
    <WithMatiksBackground>
      <JoinGame gameId={gameId} />
    </WithMatiksBackground>
  );
};

export default React.memo(WaitingForFriendContainer);
