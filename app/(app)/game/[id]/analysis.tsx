import { useLocalSearchParams } from 'expo-router';
// import NewGameAnalysis from "@/src/modules/game/pages/NewGameAnalysis/NewGameAnalysis";
import GameQuestionsDetailedAnalysis from '@/src/modules/game/pages/GameQuestionsDetailedAnalysis';

import React from 'react';

const NewGameAnalysisPage = () => {
  const searchParams = useLocalSearchParams();
  const { id: gameId } = searchParams;

  return <GameQuestionsDetailedAnalysis gameId={gameId} />;
};

export default React.memo(NewGameAnalysisPage);
