/* eslint-disable react/function-component-definition */
import '../wdyr';
import 'core/utils/polyfill';
import 'core/utils/webEngagePolyfill';
import '../tamagui-web.css';

import Bugsnag from '@bugsnag/expo';

import { TamaguiProvider } from 'tamagui';

import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';

import {
  DefaultTheme,
  ThemeProvider as NavigationThemeProvider,
} from '@react-navigation/native';
import { createTheme, ThemeProvider as RNEThemeProvider } from '@rneui/themed';

import { Slot } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { hideSplashScreen } from 'core/utils/splashScreenHelper';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';

import { useColorScheme } from 'core/hooks/useColorScheme';
import DarkTheme from 'core/constants/themes/dark';
import { ApolloProvider } from '@apollo/client';
import { ApolloClientContextProvider } from 'core/contexts/apolloClientContext';
import ErrorView from 'atoms/ErrorView';

import SessionProvider from 'modules/auth/containers/AuthProvider';
import _isNil from 'lodash/isNil';
import Loading from 'atoms/Loading';
import GoogleOAuthProvider from 'core/oauth/containers/GoogleOAuthProvider';
import useAppInitialize from 'core/hooks/appInitialization/useAppInitialize';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import useInAppUpdateChecker from 'core/hooks/useInAppUpdateChecker';
import useHandleValentineWebsite from 'core/hooks/useHandleValentineWebsite';
import { useUtmTracking } from 'core/utm-tracking/useUTMTracking';
import { initializeCrashlytics } from '@/src/core/analytics/Crashlytics';
import { OpenTypeFontProvider } from '@/src/core/contexts/OpenTypeFontContext';
import WithGrowthBookProvider from 'core/container/WithGrowthBookProvider';
import { tamaguiConfig } from '../tamagui.config';

Bugsnag.start(process.env.EXPO_PUBLIC_BUGSNAG_API_KEY);

global.Bugsnag = Bugsnag;

export { ErrorBoundary } from 'expo-router';

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: '/',
};

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync().catch((error) => {
  /* reloading the app might trigger some race conditions, ignore them */
  console.warn('Error preventing auto hide of splash screen:', error);
});

let isSplashScreenDismissed = false;

const PracticeContext = React.createContext({});

const BugSnagErrorBoundary =
  Bugsnag.getPlugin('react').createErrorBoundary(React);

export const usePracticeContext = () => useContext(PracticeContext);

export default function RootLayout() {
  useUtmTracking();
  useHandleValentineWebsite();
  useInAppUpdateChecker();
  const {
    apolloClient,
    clientCreationError,
    initializeApolloClient,
    updateApolloClient,
    isAppReady,
    cachedUser,
  } = useAppInitialize();
  const runOnceRef = useRef(false);

  useEffect(() => {
    (async () => {
      if (isAppReady && !isSplashScreenDismissed) {
        try {
          isSplashScreenDismissed = true;
          // Use our helper function to safely hide the splash screen
          await hideSplashScreen();
        } catch (e) {
          // handle errors
          console.error('Error hiding splash screen:', e);
        }
      }
    })();
  }, [isAppReady]);

  useEffect(() => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      navigator.serviceWorker.register('/service-worker.js');
    }
    initializeCrashlytics?.();
  }, []);

  if (clientCreationError) {
    return (
      <ErrorView
        errorMessage="An error occurred while connecting to server."
        onRetry={initializeApolloClient}
      />
    );
  }

  if (_isNil(apolloClient) || !isAppReady) {
    return <Loading label="Setting up" />;
  }

  return (
    <BugSnagErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <ApolloClientContextProvider value={{ updateApolloClient }}>
            <ApolloProvider client={apolloClient}>
              <RootLayoutNav cachedUser={cachedUser} client={apolloClient} />
            </ApolloProvider>
          </ApolloClientContextProvider>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </BugSnagErrorBoundary>
  );
}

const styles = StyleSheet.create({
  image: {
    flex: 1,
    resizeMode: 'cover',
    justifyContent: 'center',
  },
  overlayContainer: {
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    position: 'fixed',
  },
});

const OverlayComponents = () => {
  const ToastComponent = require('molecules/Toast').Component;
  const RightPaneComponent = require('molecules/RightPane/RightPane').Component;
  const PopoverComponent = require('molecules/Popover/Popover').Component;
  const BottomSheetComponent =
    require('molecules/BottomSheet/BottomSheet').Component;

  return (
    <>
      <ToastComponent key="ToastComponent" />
      <RightPaneComponent key="RightPaneComponent" />
      <PopoverComponent key="PopoverComponent" />
      <BottomSheetComponent key="BottomSheetComponent" />
    </>
  );
};

const RootLayoutNav = (props) => {
  const { cachedUser, client } = props;

  const [practiceConfig, setPracticeConfig] = useState();
  const [practiceSession, savePracticeSession] = useState();

  const contextValue = useMemo(
    () => ({
      practiceConfig,
      setPracticeConfig,
      practiceSession,
      savePracticeSession,
    }),
    [practiceConfig, setPracticeConfig, practiceSession, savePracticeSession],
  );

  const windowHeight = Dimensions.get('window').height;
  const colorScheme = useColorScheme();

  const customTheme = createTheme({
    components: {
      Text: {
        style: {
          fontFamily: 'Montserrat-500',
        },
      },
    },
  });

  return (
    <PracticeContext.Provider value={contextValue}>
      <SessionProvider cachedUser={cachedUser} client={client}>
        <WithGrowthBookProvider>
          <GoogleOAuthProvider>
            <OpenTypeFontProvider>
              <TamaguiProvider
                config={tamaguiConfig}
                defaultTheme={colorScheme}
              >
                <RNEThemeProvider theme={customTheme}>
                  <NavigationThemeProvider
                    value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}
                  >
                    <SafeAreaView
                      style={{
                        flex: 1,
                        height: windowHeight,
                        width: '100%',
                        backgroundColor: DarkTheme.colors.background,
                      }}
                    >
                      <Slot />
                      <View
                        style={styles.overlayContainer}
                        pointerEvents="box-none"
                      >
                        <OverlayComponents />
                      </View>
                    </SafeAreaView>
                  </NavigationThemeProvider>
                </RNEThemeProvider>
              </TamaguiProvider>
            </OpenTypeFontProvider>
          </GoogleOAuthProvider>
        </WithGrowthBookProvider>
      </SessionProvider>
    </PracticeContext.Provider>
  );
};

// export default AppLayout;
