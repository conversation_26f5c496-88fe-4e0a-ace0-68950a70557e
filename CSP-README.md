# Content Security Policy (CSP) Configuration

This document explains the Content Security Policy (CSP) configuration for the Matiks web application deployed on Vercel.

## 📋 Overview

Content Security Policy (CSP) is a security feature that helps prevent Cross-Site Scripting (XSS) attacks, code injection, and other security vulnerabilities by controlling which resources the browser is allowed to load.

## 🔧 Configuration Location

The CSP is configured in **`vercel.json`** under the headers section:

```json
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "Content-Security-Policy",
          "value": "..."
        }
      ]
    }
  ]
}
```

## 🎯 Current CSP Directives

### `default-src 'self'`
- **Purpose**: Default policy for all resource types
- **Allows**: Only resources from the same origin (matiks.com)

### `script-src`
**Allows JavaScript execution from:**
- `'self'` - Same origin scripts
- `'unsafe-inline'` - Inline scripts (required for some libraries)
- `'wasm-unsafe-eval'` - WebAssembly compilation (secure alternative to 'unsafe-eval')
- `https://widgets.in.webengage.com` - WebEngage analytics scripts
- `https://c.in.webengage.com` - WebEngage additional scripts
- `https://*.in.webengage.com` - All WebEngage subdomains
- `https://cdn.matiks.com` - Matiks CDN scripts
- `https://cdn.jsdelivr.net` - jsDelivr CDN
- `https://notify.bugsnag.com` - Bugsnag error reporting
- `https://accounts.google.com` - Google Sign-In scripts
- `https://apis.google.com` - Google API scripts
- `https://unpkg.com` - NPM package CDN (for Rive animations)

### `style-src`
**Allows CSS from:**
- `'self'` - Same origin stylesheets
- `'unsafe-inline'` - Inline styles (required for dynamic styling)
- `https://fonts.googleapis.com` - Google Fonts CSS

### `img-src`
**Allows images from:**
- `'self'` - Same origin images
- `data:` - Data URLs (base64 images)
- `blob:` - Blob URLs (generated images)
- `https:` - Any HTTPS source (for user avatars, external images)

### `font-src`
**Allows fonts from:**
- `'self'` - Same origin fonts
- `https://fonts.gstatic.com` - Google Fonts files
- `data:` - Data URL fonts

### `connect-src`
**Allows network connections to:**
- `'self'` - Same origin API calls
- `https://matiks.com` - Main domain
- `https://www.matiks.com` - WWW domain
- `https://server.matiks.com` - API server
- `https://cdn.matiks.com` - CDN resources
- `https://dev.matiks.com` - Development domain
- `https://dev.server.matiks.com` - Development API
- `https://apiv0-growthbook.matiks.com` - GrowthBook feature flags
- `https://widgets.in.webengage.com` - WebEngage API
- `https://c.in.webengage.com` - WebEngage additional API
- `https://*.in.webengage.com` - All WebEngage subdomains
- `https://api.mixpanel.com` - Mixpanel analytics
- `https://cdn.jsdelivr.net` - jsDelivr CDN
- `https://accounts.google.com` - Google OAuth
- `https://oauth2.googleapis.com` - Google OAuth API
- `https://notify.bugsnag.com` - Bugsnag error reporting
- `https://sessions.bugsnag.com` - Bugsnag session tracking
- `https://unpkg.com` - NPM package CDN
- WebSocket connections to various Matiks domains
- Localhost connections for development

### `frame-src`
**Allows embedding frames from:**
- `'self'` - Same origin frames
- `https://accounts.google.com` - Google Sign-In popup
- `https://widgets.in.webengage.com` - WebEngage widgets
- `https://*.in.webengage.co` - WebEngage frames
- `https://*.in.webengage.com` - WebEngage frames

### Other Directives
- `frame-ancestors 'self'` - Only allow embedding by same origin
- `object-src 'none'` - Block all plugins (Flash, etc.)
- `base-uri 'self'` - Restrict base URL changes
- `form-action 'self'` - Only allow form submissions to same origin

## 🛡️ Security Features

### ✅ What's Protected
- **XSS Attacks**: Blocks unauthorized script execution
- **Code Injection**: Prevents malicious code injection
- **Clickjacking**: Restricts iframe embedding
- **Data Exfiltration**: Controls where data can be sent

### ⚠️ Security Trade-offs
- `'unsafe-inline'` for scripts/styles: Required for some libraries but reduces security
- `'wasm-unsafe-eval'`: Allows WebAssembly but blocks dangerous eval() functions
- Wildcard domains: Convenient but less restrictive than specific domains

## 🔄 Adding New Services

When integrating new third-party services, you may need to update the CSP:

### 1. Identify Required Domains
Check browser console for CSP violations:
```
Refused to load the script 'https://example.com/script.js' because it violates the following Content Security Policy directive: "script-src ..."
```

### 2. Update vercel.json
Add the domain to appropriate directive(s):
```json
"script-src": "'self' ... https://example.com",
"connect-src": "'self' ... https://api.example.com"
```

### 3. Test Thoroughly
- Deploy changes
- Test all functionality
- Monitor console for new violations

## 🚨 Common Issues & Solutions

### Issue: Scripts Not Loading
**Error**: `Refused to load the script`
**Solution**: Add domain to `script-src`

### Issue: API Calls Blocked
**Error**: `Refused to connect to`
**Solution**: Add domain to `connect-src`

### Issue: Frames Not Loading
**Error**: `Refused to frame`
**Solution**: Add domain to `frame-src`

### Issue: WebAssembly Not Working
**Error**: `Refused to compile or instantiate WebAssembly`
**Solution**: Ensure `'wasm-unsafe-eval'` is in `script-src`

## 📊 Service Mapping

| Service | Purpose | Required Directives |
|---------|---------|-------------------|
| **WebEngage** | Analytics & User Engagement | script-src, connect-src, frame-src |
| **Google Sign-In** | Authentication | script-src, connect-src, frame-src |
| **Bugsnag** | Error Reporting | script-src, connect-src |
| **Mixpanel** | Analytics | connect-src |
| **GrowthBook** | Feature Flags | connect-src |
| **Rive** | Animations (WASM) | script-src (wasm-unsafe-eval), connect-src |
| **Google Fonts** | Typography | style-src, font-src |

## 🔍 Monitoring & Maintenance

### Regular Tasks
1. **Monitor CSP violations** in production
2. **Review new service integrations** for CSP requirements
3. **Update domains** when services change endpoints
4. **Test after updates** to ensure functionality

### Tools for Testing
- Browser DevTools Console
- CSP Evaluator: https://csp-evaluator.withgoogle.com/
- Report-URI: For CSP violation reporting

## 🛠️ Troubleshooting Guide

### Step-by-Step CSP Debugging

1. **Open Browser DevTools** (F12)
2. **Go to Console tab**
3. **Look for CSP violation errors** (usually in red)
4. **Identify the blocked resource** and required directive
5. **Update vercel.json** with the new domain
6. **Deploy and test**

### Example CSP Violation Messages

```
❌ Refused to load the script 'https://new-service.com/script.js'
   because it violates "script-src" directive

✅ Solution: Add https://new-service.com to script-src

❌ Refused to connect to 'https://api.new-service.com/data'
   because it violates "connect-src" directive

✅ Solution: Add https://api.new-service.com to connect-src
```

### Testing Checklist

After updating CSP, verify these work:
- [ ] User authentication (Google Sign-In)
- [ ] Analytics tracking (WebEngage, Mixpanel)
- [ ] Error reporting (Bugsnag)
- [ ] Feature flags (GrowthBook)
- [ ] Animations (Rive/WASM)
- [ ] Font loading (Google Fonts)
- [ ] All API calls to your backend

## 🎯 Best Practices

### 1. Principle of Least Privilege
- Only add domains that are absolutely necessary
- Use specific domains instead of wildcards when possible
- Regularly audit and remove unused domains

### 2. Development vs Production
- Consider separate CSP configurations for different environments
- Use stricter policies in production
- Allow localhost connections only in development

### 3. Monitoring
- Set up CSP violation reporting in production
- Monitor for new violations after deployments
- Document all CSP changes with reasons

### 4. Security vs Functionality Balance
- Prefer `'wasm-unsafe-eval'` over `'unsafe-eval'`
- Minimize use of `'unsafe-inline'` where possible
- Consider nonce-based CSP for better security

## 🚀 Future Improvements

### Potential Enhancements
1. **Nonce-based CSP**: Generate random nonces for inline scripts
2. **CSP Reporting**: Set up violation reporting endpoint
3. **Environment-specific CSP**: Different policies for dev/staging/prod
4. **Automated CSP Testing**: Include CSP validation in CI/CD

### Migration Path to Stricter CSP
1. **Phase 1**: Current configuration (functional)
2. **Phase 2**: Remove `'unsafe-inline'` where possible
3. **Phase 3**: Implement nonce-based inline script handling
4. **Phase 4**: Add CSP violation reporting

## 📞 Support

### When to Update CSP
- Adding new third-party services
- Integrating new analytics tools
- Adding new CDNs or external resources
- Changing authentication providers

### Who to Contact
- **Frontend Team**: For script/style related violations
- **Backend Team**: For API connection issues
- **DevOps Team**: For deployment and configuration
- **Security Team**: For security policy reviews

## 📚 Resources

- [MDN CSP Guide](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP)
- [CSP Quick Reference](https://content-security-policy.com/)
- [Google CSP Guide](https://developers.google.com/web/fundamentals/security/csp)
- [CSP Evaluator Tool](https://csp-evaluator.withgoogle.com/)
- [Vercel Headers Documentation](https://vercel.com/docs/projects/project-configuration#headers)

---

**Last Updated**: December 2024
**Maintained By**: Development Team
**Next Review**: March 2025
