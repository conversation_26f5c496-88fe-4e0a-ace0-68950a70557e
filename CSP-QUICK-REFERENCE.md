# CSP Quick Reference Card

## 🚨 Common CSP Violations & Quick Fixes

### Script Loading Issues
```
❌ Refused to load the script 'https://example.com/script.js'
✅ Add to script-src: https://example.com
```

### API Connection Issues
```
❌ Refused to connect to 'https://api.example.com'
✅ Add to connect-src: https://api.example.com
```

### Frame/Iframe Issues
```
❌ Refused to frame 'https://widget.example.com'
✅ Add to frame-src: https://widget.example.com
```

### WebAssembly Issues
```
❌ Refused to compile or instantiate WebAssembly module
✅ Ensure 'wasm-unsafe-eval' is in script-src
```

## 🔧 Quick Fix Template

1. **Find the error** in browser console
2. **Identify the domain** from the error message
3. **Update vercel.json**:
   ```json
   "script-src": "'self' ... https://NEW-DOMAIN",
   "connect-src": "'self' ... https://NEW-API-DOMAIN"
   ```
4. **Deploy and test**

## 📋 Current Service Domains

### Analytics & Tracking
- WebEngage: `*.in.webengage.com`, `*.in.webengage.co`
- Mixpanel: `api.mixpanel.com`
- Bugsnag: `notify.bugsnag.com`, `sessions.bugsnag.com`

### Authentication
- Google: `accounts.google.com`, `apis.google.com`, `oauth2.googleapis.com`

### Feature Management
- GrowthBook: `apiv0-growthbook.matiks.com`

### CDNs & Resources
- jsDelivr: `cdn.jsdelivr.net`
- unpkg: `unpkg.com`
- Google Fonts: `fonts.googleapis.com`, `fonts.gstatic.com`

### App Domains
- Main: `matiks.com`, `www.matiks.com`
- API: `server.matiks.com`, `dev.server.matiks.com`
- CDN: `cdn.matiks.com`

## 🛡️ Security Levels

### Most Secure → Least Secure
1. `'self'` - Same origin only
2. `https://specific-domain.com` - Specific domain
3. `https://*.domain.com` - Wildcard subdomain
4. `'wasm-unsafe-eval'` - WASM only
5. `'unsafe-inline'` - Inline scripts/styles
6. `'unsafe-eval'` - All eval functions (avoid!)

## 🚀 Emergency CSP Disable

**⚠️ ONLY FOR DEBUGGING - NEVER IN PRODUCTION**

Temporarily comment out CSP in vercel.json:
```json
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        // {
        //   "key": "Content-Security-Policy",
        //   "value": "..."
        // }
      ]
    }
  ]
}
```

**Remember to re-enable after debugging!**

## 📞 Quick Contacts

- **CSP Issues**: Frontend Team
- **API Blocks**: Backend Team  
- **Deployment**: DevOps Team
- **Security Review**: Security Team

---
*Keep this handy for quick CSP troubleshooting!*
